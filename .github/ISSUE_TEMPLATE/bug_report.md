---
name: Bug report
about: Create a report to help us improve
title: ''
labels: ''
assignees: ''
---

# Bug Report

## Important Notes

- **Before submitting a bug report**: Please check the Issues or Discussions section to see if a similar issue or feature request has already been posted. It's likely we're already tracking it! If you’re unsure, start a discussion post first. This will help us efficiently focus on improving the project.

- **Collaborate respectfully**: We value a constructive attitude, so please be mindful of your communication. If negativity is part of your approach, our capacity to engage may be limited. We’re here to help if you’re open to learning and communicating positively. Remember, Open WebUI is a volunteer-driven project managed by a single maintainer and supported by contributors who also have full-time jobs. We appreciate your time and ask that you respect ours.

- **Contributing**: If you encounter an issue, we highly encourage you to submit a pull request or fork the project. We actively work to prevent contributor burnout to maintain the quality and continuity of Open WebUI.

- **Bug reproducibility**: If a bug cannot be reproduced with a `:main` or `:dev` Docker setup, or a pip install with Python 3.11, it may require additional help from the community. In such cases, we will move it to the "issues" Discussions section due to our limited resources. We encourage the community to assist with these issues. Remember, it’s not that the issue doesn’t exist; we need your help!

Note: Please remove the notes above when submitting your post. Thank you for your understanding and support!

---

## Installation Method

[Describe the method you used to install the project, e.g., git clone, Docker, pip, etc.]

## Environment

- **Open WebUI Version:** [e.g., v0.3.11]
- **Ollama (if applicable):** [e.g., v0.2.0, v0.1.32-rc1]

- **Operating System:** [e.g., Windows 10, macOS Big Sur, Ubuntu 20.04]
- **Browser (if applicable):** [e.g., Chrome 100.0, Firefox 98.0]

**Confirmation:**

- [ ] I have read and followed all the instructions provided in the README.md.
- [ ] I am on the latest version of both Open WebUI and Ollama.
- [ ] I have included the browser console logs.
- [ ] I have included the Docker container logs.
- [ ] I have provided the exact steps to reproduce the bug in the "Steps to Reproduce" section below.

## Expected Behavior:

[Describe what you expected to happen.]

## Actual Behavior:

[Describe what actually happened.]

## Description

**Bug Summary:**
[Provide a brief but clear summary of the bug]

## Reproduction Details

**Steps to Reproduce:**
[Outline the steps to reproduce the bug. Be as detailed as possible.]

## Logs and Screenshots

**Browser Console Logs:**
[Include relevant browser console logs, if applicable]

**Docker Container Logs:**
[Include relevant Docker container logs, if applicable]

**Screenshots/Screen Recordings (if applicable):**
[Attach any relevant screenshots to help illustrate the issue]

## Additional Information

[Include any additional details that may help in understanding and reproducing the issue. This could include specific configurations, error messages, or anything else relevant to the bug.]

## Note

If the bug report is incomplete or does not follow the provided instructions, it may not be addressed. Please ensure that you have followed the steps outlined in the README.md and troubleshooting.md documents, and provide all necessary information for us to reproduce and address the issue. Thank you!
