# Changelog Entry

### Description

- [Concisely describe the changes made in this pull request, including any relevant motivation and impact (e.g., fixing a bug, adding a feature, or improving performance)]

### Added

- [List any new features, functionalities, or additions]

### Changed

- [List any changes, updates, refactorings, or optimizations]

### Deprecated

- [List any deprecated functionality or features that have been removed]

### Removed

- [List any removed features, files, or functionalities]

### Fixed

- [List any fixes, corrections, or bug fixes]

### Security

- [List any new or updated security-related changes, including vulnerability fixes]

### Breaking Changes

- **BREAKING CHANGE**: [List any breaking changes affecting compatibility or functionality]

---

### Additional Information

- [Insert any additional context, notes, or explanations for the changes]
  - [Reference any related issues, commits, or other relevant information]

### Screenshots or Videos

- [Attach any relevant screenshots or videos demonstrating the changes]
