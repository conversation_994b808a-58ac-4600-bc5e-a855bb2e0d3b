# Ollama URL for the backend to connect
# The path '/ollama' will be redirected to the specified backend URL
OLLAMA_BASE_URL='http://localhost:11434'

OPENAI_API_BASE_URL=''
OPENAI_API_KEY=''

# AUTOMATIC1111_BASE_URL="http://localhost:7860"

# DO NOT TRACK
SCARF_NO_ANALYTICS=true
DO_NOT_TRACK=true
ANONYMIZED_TELEMETRY=false

# GSAI CUSTOM FLAGS

ALLOW_SIMULTANEOUS_MODELS=false
DEFAULT_SHOW_CHANGELOG=false # Sets default value for whether or not to display the changelog
ENABLE_CHAT_CONTROLS=false
ENABLE_ONBOARDING_PAGE=false # Sets whether or not to display the initial onboarding splash page/slideshow
ENABLE_SET_AS_DEFAULT_MODEL=false

# Datadog Monitoring
PUBLIC_DATADOG_APP_ID=''
PUBLIC_DATADOG_CLIENT_TOKEN=''
PUBLIC_DATADOG_BROWSERLOGS_CLIENT_TOKEN=''
PUBLIC_DATADOG_SERVICE=''

# Globally set default value for whether or not display the version update (admin user can change individual user setting on UI)
DEFAULT_SHOW_VERSION_UPDATE=false

# Set wether or not to display Active Users count on user menu
ENABLE_ACTIVE_USERS_COUNT=false

# shows/hides evaluations > feedbacks tab in admin panel
ENABLE_ADMIN_FEEDBACKS=false

#show/hide Record voice and call buttons in chat (MessageInput)
ENABLE_RECORD_VOICE_AND_CALL=false

# Enables more inputs in chat (e.g., "Capture" and "Upload Files") (default: false)
ENABLE_MORE_INPUTS=false

# show/hide disclaimer at the bottm (default = true)
ENABLE_DISCLAIMER=true

# show/hide sidebar search (default":False)
ENABLE_SIDEBAR_SEARCH =false

# show/hide sidebar create folder (default":False)
ENABLE_SIDEBAR_CREATE_FOLDER =false


# show/hide floating buttons (ask and explain) in response message (default:false)
ENABLE_FLOATING_BUTTONS=false

# Show/hide the Delete button in the chat menu (default: false)
ENABLE_DELETE_BUTTON=false
# Note: If you disable the delete button, you might also want to set
# USER_PERMISSIONS_CHAT_DELETE=false to disable user chat deletion via the API.
USER_PERMISSIONS_CHAT_DELETE=false

# show/hide bottom sidebar user profile (default:false)
ENABLE_SIDEBAR_USER_PROFILE=false

# show/hide chat message input top logo (line above the chat message input - default it to false)
ENABLE_MESSAGE_INPUT_LOGO=false

# show/hide prompt suggestions
ENABLE_PROMPT_SUGGESTIONS=true

# show/hide the user settings menu
ENABLE_USER_SETTINGS_MENU=false

# Show the search bar in the Model Selector dropdown (default: false)
ENABLE_MODEL_SELECTOR_SEARCH=false

# show/hide ui controls under the response
ENABLE_RESPONSE_PROMPT_EDIT=false
ENABLE_RESPONSE_CONTINUE=false

# show/hide screen capture from message input
ENABLE_SCREEN_CAPTURE=false
