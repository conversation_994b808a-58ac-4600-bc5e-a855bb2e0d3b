#!/bin/bash
git rm -r --cached build/ >/dev/null 2>&1 || true

echo "Scanning commit... 30-60s 💅🥱⏳"

if ! gitleaks protect --staged -v --redact; then
    echo "Gitleaks detected secrets in staged files"
    exit 1
fi

git add .secrets.baseline
if (git diff --staged --name-only -z | xargs -0 detect-secrets-hook --baseline .secrets.baseline) | grep -q "Secret Type"; then
    echo "$(git diff --staged --name-only -z | xargs -0 detect-secrets-hook --baseline .secrets.baseline)"
    echo "Detect-secrets detected secrets in staged files"
    exit 1
fi

echo "No secrets detected in staged files! 🎉"

if ! git diff --quiet; then
    echo "Error: You have unstaged changes."
    echo "The formatter in this pre-commit hook requires all changes to be staged."
    echo "Tip: Use 'git stash --keep-index' to temporarily store your unstaged changes,"
    echo "     then commit your staged changes, and finally 'git stash pop'"
    exit 1
fi

npm install
npm run format
npm run format:backend
npm run i18n:parse

git add .