{"-1 for no limit, or a positive integer for a specific limit": "", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' או '-1' ללא תפוגה.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "", "(e.g. `sh webui.sh --api`)": "(למשל `sh webui.sh --api`)", "(latest)": "(הא<PERSON><PERSON><PERSON><PERSON>)", "{{ models }}": "{{ ד<PERSON><PERSON>ים }}", "{{COUNT}} Replies": "", "{{user}}'s Chats": "צ'אטים של {{user}}", "{{webUIName}} Backend Required": "נדרש Backend של {{webUIName}}", "*Prompt node ID(s) are required for image generation": "", "A new version (v{{LATEST_VERSION}}) is now available.": "", "A task model is used when performing tasks such as generating titles for chats and web search queries": "מודל משימה משמש בעת ביצוע משימות כגון יצירת כותרות עבור צ'אטים ושאילתות חיפוש באינטרנט", "a user": "משת<PERSON>ש", "About": "אודות", "Access": "", "Access Control": "", "Accessible to all users": "", "Account": "<PERSON><PERSON><PERSON><PERSON>ן", "Account Activation Pending": "", "Actions": "", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "", "Active Users": "", "Add": "הוסף", "Add a model ID": "", "Add a short description about what this model does": "הוסף תיאור קצר אודות אופן הפעולה של מודל זה", "Add a tag": "הוסף תג", "Add Arena Model": "", "Add Connection": "", "Add Content": "", "Add content here": "", "Add custom prompt": "הוסף פקודה מותאמת אישית", "Add Files": "הוסף קבצים", "Add Group": "", "Add Memory": "הוסף זיכרון", "Add Model": "הוסף מודל", "Add Reaction": "", "Add Tag": "", "Add Tags": "הוסף תגים", "Add text content": "", "Add User": "הוסף משתמש", "Add User Group": "", "Adjusting these settings will apply changes universally to all users.": "התאמת הגדרות אלו תחול על כל המשתמשים.", "admin": "מנהל", "Admin": "", "Admin Panel": "לוח בקרה למנהל", "Admin Settings": "הגדרות מנהל", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "", "Advanced Parameters": "פרמטרים מתקדמים", "Advanced Params": "פרמטרים מתקדמים", "All Documents": "כל המסמכים", "All models deleted successfully": "", "Allow Chat Delete": "", "Allow Chat Deletion": "אפשר מחיקת צ'אט", "Allow Chat Edit": "", "Allow File Upload": "", "Allow non-local voices": "", "Allow Temporary Chat": "", "Allow User Location": "", "Allow Voice Interruption in Call": "", "Allowed Endpoints": "", "Already have an account?": "כבר יש לך חשבון?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out. (Default: 0.0)": "", "an assistant": "עוזר", "and": "וגם", "and {{COUNT}} more": "", "and create a new shared link.": "וצור קישור משותף חדש.", "API Base URL": "כתובת URL בסיסית ל-API", "API Key": "מפתח API", "API Key created.": "מפתח API נוצר.", "API Key Endpoint Restrictions": "", "API keys": "מפתחות API", "Application DN": "", "Application DN Password": "", "applies to all users with the \"user\" role": "", "April": "אפריל", "Archive": "אר<PERSON><PERSON><PERSON>ן", "Archive All Chats": "אח<PERSON><PERSON> באר<PERSON><PERSON>ון את כל הצ'אטים", "Archived Chats": "צ'אטים מאורכבים", "archived-chat-export": "", "Are you sure you want to delete this channel?": "", "Are you sure you want to delete this message?": "", "Are you sure you want to unarchive all archived chats?": "", "Are you sure?": "האם אתה בטוח?", "Arena Models": "", "Artifacts": "", "Ask a question": "", "Assistant": "", "Attach file": "צרף קובץ", "Attribute for Username": "", "Audio": "אודיו", "August": "אוגוסט", "Authenticate": "", "Auto-Copy Response to Clipboard": "העתקה אוטומטית של תגובה ללוח", "Auto-playback response": "תגובת השמעה אוטומטית", "Autocomplete Generation": "", "Autocomplete Generation Input Max Length": "", "Automatic1111": "", "AUTOMATIC1111 Api Auth String": "", "AUTOMATIC1111 Base URL": "כתובת URL בסיסית של AUTOMATIC1111", "AUTOMATIC1111 Base URL is required.": "נדרשת כתובת URL בסיסית של AUTOMATIC1111", "Available list": "", "available!": "זמין!", "Azure AI Speech": "", "Azure Region": "", "Back": "חז<PERSON>ר", "Bad": "", "Bad Response": "תגובה שגויה", "Banners": "באנרים", "Base Model (From)": "דגם בסיס (מ)", "Batch Size (num_batch)": "", "before": "לפני", "Beta": "", "BETA": "", "Bing Search V7 Endpoint": "", "Bing Search V7 Subscription Key": "", "Brave Search API Key": "מפתח API של חיפוש אמיץ", "By {{name}}": "", "Bypass SSL verification for Websites": "עקוף אימות SSL עבור אתרים", "Call": "", "Call feature is not supported when using Web STT engine": "", "Camera": "", "Cancel": "בטל", "Capabilities": "יכולות", "Capture": "", "Certificate Path": "", "Change Password": "שנה סיסמה", "Channel Name": "", "Channels": "", "Character": "", "Character limit for autocomplete generation input": "", "Chart new frontiers": "", "Chat": "צ'אט", "Chat Background Image": "", "Chat Bubble UI": "UI של תיבת הדיבור", "Chat Controls": "", "Chat direction": "כיוון צ'אט", "Chat Overview": "", "Chat Permissions": "", "Chat Tags Auto-Generation": "", "Chats": "צ'אטים", "Check Again": "<PERSON><PERSON><PERSON><PERSON> שוב", "Check for updates": "בד<PERSON><PERSON> עדכונים", "Checking for updates...": "בודק עדכונים...", "Choose a model before saving...": "בחר מודל לפני השמירה...", "Chunk Overlap": "חפי<PERSON>ת נתונים", "Chunk Params": "פרמטרי נתונים", "Chunk Size": "גודל נתונים", "Ciphers": "", "Citation": "ציטוט", "Clear memory": "", "click here": "", "Click here for filter guides.": "", "Click here for help.": "לחץ כאן לעזרה.", "Click here to": "לחץ כאן כדי", "Click here to download user import template file.": "", "Click here to learn more about faster-whisper and see the available models.": "", "Click here to select": "לחץ כאן לבחירה", "Click here to select a csv file.": "לחץ כאן לבחירת קובץ csv.", "Click here to select a py file.": "", "Click here to upload a workflow.json file.": "", "click here.": "לחץ כאן.", "Click on the user role button to change a user's role.": "לחץ על כפתור תפקיד המשתמש כדי לשנות את תפקיד המשתמש.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "", "Clone": "שיבוט", "Close": "סגור", "Code execution": "", "Code formatted successfully": "", "Collection": "אוסף", "Color": "", "ComfyUI": "ComfyUI", "ComfyUI API Key": "", "ComfyUI Base URL": "כתובת URL בסיסית של ComfyUI", "ComfyUI Base URL is required.": "נדרשת כתובת URL בסיסית של ComfyUI", "ComfyUI Workflow": "", "ComfyUI Workflow Nodes": "", "Command": "פקודה", "Completions": "", "Concurrent Requests": "בקשות בו-זמניות", "Configure": "", "Configure Models": "", "Confirm": "", "Confirm Password": "א<PERSON>ר סיסמה", "Confirm your action": "", "Confirm your new password": "", "Connections": "חיבורים", "Contact Admin for WebUI Access": "", "Content": "תו<PERSON><PERSON>", "Content Extraction": "", "Context Length": "אורך הקשר", "Continue Response": "המשך תגובה", "Continue with {{provider}}": "", "Continue with Email": "", "Continue with LDAP": "", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "", "Controls": "", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text. (Default: 5.0)": "", "Copied": "", "Copied shared chat URL to clipboard!": "העתקת כתובת URL של צ'אט משותף ללוח!", "Copied to clipboard": "", "Copy": "העתק", "Copy last code block": "העתק את בלוק הקוד האחרון", "Copy last response": "העתק את התגובה האחרונה", "Copy Link": "העת<PERSON> קישור", "Copy to clipboard": "", "Copying to clipboard was successful!": "ההעתקה ללוח הייתה מוצלחת!", "Create": "", "Create a knowledge base": "", "Create a model": "יצירת מודל", "Create Account": "<PERSON><PERSON><PERSON>ן", "Create Admin Account": "", "Create Channel": "", "Create Group": "", "Create Knowledge": "", "Create new key": "צור מפתח חדש", "Create new secret key": "צור מפתח סודי חדש", "Created at": "נוצר ב", "Created At": "נוצר ב", "Created by": "", "CSV Import": "", "Current Model": "המודל הנוכחי", "Current Password": "הסיס<PERSON>ה הנוכחית", "Custom": "מותאם אישית", "Dark": "כהה", "Database": "מסד נתונים", "December": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Default": "ברירת מחדל", "Default (Open AI)": "", "Default (SentenceTransformers)": "ברירת מחדל (SentenceTransformers)", "Default Model": "מודל ברירת מחדל", "Default model updated": "המודל המוגדר כברירת מחדל עודכן", "Default Models": "", "Default permissions": "", "Default permissions updated successfully": "", "Default Prompt Suggestions": "הצעות ברירת מחדל לפקודות", "Default to 389 or 636 if TLS is enabled": "", "Default to ALL": "", "Default User Role": "תפקיד משת<PERSON>ש ברירת מחדל", "Delete": "מחק", "Delete a model": "<PERSON><PERSON><PERSON> מודל", "Delete All Chats": "מחק את כל הצ'אטים", "Delete All Models": "", "Delete chat": "מחק צ'אט", "Delete Chat": "מחק צ'אט", "Delete chat?": "", "Delete folder?": "", "Delete function?": "", "Delete Message": "", "Delete prompt?": "", "delete this link": "מחק את הקישור הזה", "Delete tool?": "", "Delete User": "מח<PERSON> משתמש", "Deleted {{deleteModelTag}}": "נמחק {{deleteModelTag}}", "Deleted {{name}}": "נמחק {{name}}", "Deleted User": "", "Describe your knowledge base and objectives": "", "Description": "תיאור", "Disabled": "", "Discover a function": "", "Discover a model": "גלה מודל", "Discover a prompt": "גלה פקודה", "Discover a tool": "", "Discover wonders": "", "Discover, download, and explore custom functions": "", "Discover, download, and explore custom prompts": "גלה, הורד, וחק<PERSON>ר פקודות מותאמות אישית", "Discover, download, and explore custom tools": "", "Discover, download, and explore model presets": "גלה, הורד, וחקור הגדרות מודל מוגדרות מראש", "Dismissible": "", "Display": "", "Display Emoji in Call": "", "Display the username instead of You in the Chat": "הצג את שם המשתמש במקום 'אתה' בצ'אט", "Displays citations in the response": "", "Dive into knowledge": "", "Do not install functions from sources you do not fully trust.": "", "Do not install tools from sources you do not fully trust.": "", "Document": "מסמך", "Documentation": "", "Documents": "מסמכים", "does not make any external connections, and your data stays securely on your locally hosted server.": "לא מבצע חיבורים חיצוניים, והנתונים שלך נשמרים באופן מאובטח בשרת המקומי שלך.", "Don't have an account?": "אין לך חשבון?", "don't install random functions from sources you don't trust.": "", "don't install random tools from sources you don't trust.": "", "Done": "", "Download": "הורד", "Download canceled": "ההורדה בוטלה", "Download Database": "הורד מסד נתונים", "Drag and drop a file to upload or select a file to view": "", "Draw": "", "Drop any files here to add to the conversation": "גרור כל קובץ לכאן כדי להוסיף לשיחה", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "למשל '30s', '10m'. יחידות זמן חוקיות הן 's', 'm', 'h'.", "e.g. A filter to remove profanity from text": "", "e.g. My Filter": "", "e.g. My Tools": "", "e.g. my_filter": "", "e.g. my_tools": "", "e.g. Tools for performing various operations": "", "Edit": "ערוך", "Edit Arena Model": "", "Edit Channel": "", "Edit Connection": "", "Edit Default Permissions": "", "Edit Memory": "", "Edit User": "ערוך משתמש", "Edit User Group": "", "ElevenLabs": "", "Email": "דוא\"ל", "Embark on adventures": "", "Embedding Batch Size": "", "Embedding Model": "מודל הטמעה", "Embedding Model Engine": "מנוע מודל הטמעה", "Embedding model set to \"{{embedding_model}}\"": "מודל ההטמעה הוגדר ל-\"{{embedding_model}}\"", "Enable API Key": "", "Enable autocomplete generation for chat messages": "", "Enable Community Sharing": "הפיכת שיתוף קהילה לזמין", "Enable Google Drive": "", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "", "Enable Message Rating": "", "Enable Mirostat sampling for controlling perplexity. (Default: 0, 0 = Disabled, 1 = Mirostat, 2 = Mirostat 2.0)": "", "Enable New Sign Ups": "אפשר הרשמות חדשות", "Enable Web Search": "הפיכת חיפוש באינטרנט לזמין", "Enabled": "", "Engine": "", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "ודא שקובץ ה-CSV שלך כולל 4 עמודות בסדר הבא: שם, דוא\"ל, סיסמה, תפקיד.", "Enter {{role}} message here": "הזן הודעת {{role}} כאן", "Enter a detail about yourself for your LLMs to recall": "הזן פרטים על עצמך כדי שLLMs יזכור", "Enter api auth string (e.g. username:password)": "", "Enter Application DN": "", "Enter Application DN Password": "", "Enter Bing Search V7 Endpoint": "", "Enter Bing Search V7 Subscription Key": "", "Enter Brave Search API Key": "הזן מפתח API של חיפוש אמיץ", "Enter certificate path": "", "Enter CFG Scale (e.g. 7.0)": "", "Enter Chunk Overlap": "הזן חפיפת נתונים", "Enter Chunk Size": "הזן גודל נתונים", "Enter description": "", "Enter Github Raw URL": "הזן כתובת URL של Github Raw", "Enter Google PSE API Key": "הזן מפתח API של Google PSE", "Enter Google PSE Engine Id": "הזן את מזהה מנוע PSE של Google", "Enter Image Size (e.g. 512x512)": "הזן גודל תמונה (למשל 512x512)", "Enter Jina API Key": "", "Enter Kagi Search API Key": "", "Enter language codes": "הזן קודי שפה", "Enter Model ID": "", "Enter model tag (e.g. {{modelTag}})": "הזן תג מודל (למ<PERSON><PERSON> {{modelTag}})", "Enter Mojeek Search API Key": "", "Enter name or email": "", "Enter Number of Steps (e.g. 50)": "הזן מספר שלבים (למ<PERSON><PERSON> 50)", "Enter proxy URL (e.g. **************************:port)": "", "Enter Sampler (e.g. Euler a)": "", "Enter Scheduler (e.g. Karras)": "", "Enter Score": "הזן ציון", "Enter SearchApi API Key": "", "Enter SearchApi Engine": "", "Enter Searxng Query URL": "הזן כתובת URL של שאילתת Searxng", "Enter Seed": "", "Enter Serper API Key": "הזן מפתח API של Serper", "Enter Serply API Key": "", "Enter Serpstack API Key": "הזן מפתח API של Serpstack", "Enter server host": "", "Enter server label": "", "Enter server port": "", "Enter stop sequence": "הזן רצף עצירה", "Enter system prompt": "", "Enter Tavily API Key": "", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter Tika Server URL": "", "Enter Top K": "הזן Top K", "Enter URL (e.g. http://127.0.0.1:7860/)": "הזן כתובת URL (<PERSON><PERSON><PERSON><PERSON> http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "הזן כתובת URL (ל<PERSON><PERSON><PERSON> http://localhost:11434)", "Enter your current password": "", "Enter Your Email": "הזן את דוא\"ל שלך", "Enter Your Full Name": "הזן את שמך המלא", "Enter your message": "", "Enter your new password": "", "Enter Your Password": "הזן את הסיסמה שלך", "Enter your prompt": "", "Enter Your Role": "הזן את התפקיד שלך", "Enter Your Username": "", "Enter your webhook URL": "", "Error": "שגיאה", "ERROR": "", "Error accessing Google Drive: {{error}}": "", "Error uploading file: {{error}}": "", "Evaluations": "", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "", "Example: ALL": "", "Example: ou=users,dc=foo,dc=example": "", "Example: sAMAccountName or uid or userPrincipalName": "", "Exclude": "", "Experimental": "ניסיוני", "Explore the cosmos": "", "Export": "ייצא", "Export All Archived Chats": "", "Export All Chats (All Users)": "ייצוא כל הצ'אטים (כל המשתמשים)", "Export chat (.json)": "", "Export Chats": "ייצוא צ'אטים", "Export Config to JSON File": "", "Export Functions": "", "Export Models": "ייצוא מודלים", "Export Presets": "", "Export Prompts": "ייצוא פקודות", "Export to CSV": "", "Export Tools": "", "External Models": "", "Extremely bad": "", "Failed to add file.": "", "Failed to create API Key.": "יצירת מפתח API נכשלה.", "Failed to read clipboard contents": "קריאת תוכן הלוח נכשלה", "Failed to save models configuration": "", "Failed to update settings": "", "February": "פבר<PERSON><PERSON><PERSON>", "Feedback History": "", "Feedbacks": "", "File": "", "File added successfully.": "", "File content updated successfully.": "", "File Mode": "מצב קובץ", "File not found.": "הקובץ לא נמצא.", "File removed successfully.": "", "File size should not exceed {{maxSize}} MB.": "", "File uploaded successfully": "", "Files": "", "Filter is now globally disabled": "", "Filter is now globally enabled": "", "Filters": "", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "התגלתה הזיית טביעת אצבע: לא ניתן להשתמש בראשי תיבות כאווטאר. משתמש בתמונת פרופיל ברירת מחדל.", "Fluidly stream large external response chunks": "שידור נתונים חיצוניים בקצב רציף", "Focus chat input": "מיקוד הקלט לצ'אט", "Folder deleted successfully": "", "Folder name cannot be empty": "", "Folder name cannot be empty.": "", "Folder name updated successfully": "", "Forge new paths": "", "Form": "", "Format your variables using brackets like this:": "", "Frequency Penalty": "עונש תדירות", "Function": "", "Function created successfully": "", "Function deleted successfully": "", "Function Description": "", "Function ID": "", "Function is now globally disabled": "", "Function is now globally enabled": "", "Function Name": "", "Function updated successfully": "", "Functions": "", "Functions allow arbitrary code execution": "", "Functions allow arbitrary code execution.": "", "Functions imported successfully": "", "General": "כללי", "General Settings": "הגדרות כלליות", "Generate Image": "", "Generating search query": "יצירת שאילתת חיפוש", "Get started": "", "Get started with {{WEBUI_NAME}}": "", "Global": "", "Good Response": "תגובה טובה", "Google Drive": "", "Google PSE API Key": "מפתח API של Google PSE", "Google PSE Engine Id": "מזהה מנוע PSE של Google", "Group created successfully": "", "Group deleted successfully": "", "Group Description": "", "Group Name": "", "Group updated successfully": "", "Groups": "", "GSA Chat can make mistakes. Review all responses for accuracy.": "", "h:mm a": "h:mm a", "Haptic Feedback": "", "Harmful or offensive": "", "has no conversations.": "אין שיחות.", "Hello, {{name}}": "שלום, {{name}}", "Help": "עזרה", "Help us create the best community leaderboard by sharing your feedback history!": "", "Hex Color": "", "Hex Color - Leave empty for default color": "", "Hide": "הסתר", "Host": "", "How can I help you today?": "כיצד אוכל לעזור לך היום?", "How would you rate this response?": "", "Hybrid Search": "<PERSON>י<PERSON><PERSON><PERSON> היברידי", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "", "ID": "", "Ignite curiosity": "", "Image Compression": "", "Image Generation (Experimental)": "יצירת תמונות (ניסיוני)", "Image Generation Engine": "מנוע יצירת תמונות", "Image Max Compression Size": "", "Image Settings": "הגדרות תמונה", "Images": "תמונות", "Import Chats": "יבוא צ'אטים", "Import Config from JSON File": "", "Import Functions": "", "Import Models": "ייבו<PERSON> דגמים", "Import Presets": "", "Import Prompts": "יבוא פקודות", "Import Tools": "", "Include": "", "Include `--api-auth` flag when running stable-diffusion-webui": "", "Include `--api` flag when running stable-diffusion-webui": "כלול את הדגל `--api` בעת הרצת stable-diffusion-webui", "Incomplete response": "", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive. (Default: 0.1)": "", "Info": "מידע", "Input commands": "פקודות קלט", "Install from Github URL": "התקן מכתובת URL של Github", "Instant Auto-Send After Voice Transcription": "", "Interface": "<PERSON><PERSON><PERSON><PERSON>", "Invalid file format.": "", "Invalid Tag": "תג לא חוקי", "is typing...": "", "January": "ינו<PERSON>ר", "Jina API Key": "", "join our Discord for help.": "הצטרף ל-Discord שלנו לעזרה.", "JSON": "JSON", "JSON Preview": "תצוגה מקדימה של JSON", "July": "יולי", "June": "יוני", "JWT Expiration": "תפוגת JWT", "JWT Token": "אסימון JWT", "Kagi Search API Key": "", "Keep Alive": "<PERSON><PERSON><PERSON><PERSON> פעיל", "Key": "", "Keyboard shortcuts": "קיצורי מקלדת", "Knowledge": "", "Knowledge Access": "", "Knowledge created successfully.": "", "Knowledge deleted successfully.": "", "Knowledge reset successfully.": "", "Knowledge updated successfully": "", "Label": "", "Landing Page Mode": "", "Language": "שפה", "Last Active": "פעיל לאחרונה", "Last Modified": "", "Last reply": "", "Latest users": "", "LDAP": "", "LDAP server updated": "", "Leaderboard": "", "Leave empty for unlimited": "", "Leave empty to include all models from \"{{URL}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{URL}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "", "Leave empty to use the default prompt, or enter a custom prompt": "", "Light": "<PERSON><PERSON><PERSON><PERSON>", "Listening...": "", "Local": "", "Local Models": "", "Lost": "", "LTR": "LTR", "Made by OpenWebUI Community": "נוצר על ידי קהילת OpenWebUI", "Make sure to enclose them with": "ודא להקיף אותם עם", "Make sure to export a workflow.json file as API format from ComfyUI.": "", "Manage": "", "Manage Arena Models": "", "Manage Ollama": "", "Manage Ollama API Connections": "", "Manage OpenAI API Connections": "", "Manage Pipelines": "ניהול צינורות", "March": "מרץ", "Max Tokens (num_predict)": "מקסימום אסימונים (num_predict)", "Max Upload Count": "", "Max Upload Size": "", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "ניתן להוריד מקסימום 3 מודלים בו זמנית. אנא נסה שוב מאוחר יותר.", "May": "מאי", "Memories accessible by LLMs will be shown here.": "מזכירים נגישים על ידי LLMs יוצגו כאן.", "Memory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Memory added successfully": "", "Memory cleared successfully": "", "Memory deleted successfully": "", "Memory updated successfully": "", "Merge Responses": "", "Message rating should be enabled to use this feature": "", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "הודעות שתשלח לאחר יצירת הקישור לא ישותפו. משתמשים עם כתובת האתר יוכלו לצפות בצ'אט המשותף.", "Min P": "", "Minimum Score": "<PERSON><PERSON><PERSON><PERSON> מינימלי", "Mirostat": "Mirostat", "Mirostat Eta": "Mirostat Eta", "Mirostat Tau": "Mirostat Tau", "MMMM DD, YYYY": "DD בMMMM, YYYY", "MMMM DD, YYYY HH:mm": "DD בMMMM, YYYY HH:mm", "MMMM DD, YYYY hh:mm:ss A": "", "Model": "", "Model '{{modelName}}' has been successfully downloaded.": "המודל '{{modelName}}' הורד בהצלחה.", "Model '{{modelTag}}' is already in queue for downloading.": "המודל '{{modelTag}}' כבר בתור להורדה.", "Model {{modelId}} not found": "המודל {{modelId}} לא נמצא", "Model {{modelName}} is not vision capable": "דגם {{modelName}} אינו בעל יכולת ראייה", "Model {{name}} is now {{status}}": "דגם {{name}} הוא כעת {{status}}", "Model accepts image inputs": "", "Model created successfully!": "", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "נתיב מערכת הקבצים של המודל זוהה. נדרש שם קצר של המודל לעדכון, לא ניתן להמשיך.", "Model Filtering": "", "Model ID": "מזהה דגם", "Model IDs": "", "Model Name": "", "Model not selected": "לא נבחר מודל", "Model Params": "פרמ<PERSON> מודל", "Model Permissions": "", "Model updated successfully": "", "Modelfile Content": "תוכן קובץ מודל", "Models": "מודלים", "Models Access": "", "Models configuration saved successfully": "", "Mojeek Search API Key": "", "more": "", "More": "עוד", "Name": "שם", "Name your knowledge base": "", "New Chat": "צ'אט חדש", "New folder": "", "New Password": "סיסמה חדשה", "new-channel": "", "No content found": "", "No content to speak": "", "No distance available": "", "No feedbacks found": "", "No file selected": "", "No files found.": "", "No groups with access, add a group to grant access": "", "No HTML, CSS, or JavaScript content found.": "", "No knowledge found": "", "No model IDs": "", "No models found": "", "No models selected": "", "No results found": "לא נמצאו תוצאות", "No search query generated": "לא נוצרה שאילתת חיפוש", "No source available": "<PERSON><PERSON>ן <PERSON><PERSON> זמין", "No users were found.": "", "No valves to update": "", "None": "ללא", "Not accurate": "", "Not relevant": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "הערה: אם תקבע ציון מינימלי, החיפוש יחזיר רק מסמכים עם ציון שגבוה או שווה לציון המינימלי.", "Notes": "", "Notification Sound": "", "Notification Webhook": "", "Notifications": "התראות", "November": "נובמ<PERSON>ר", "num_gpu (Ollama)": "", "num_thread (Ollama)": "num_thread (Ollama)", "OAuth ID": "", "October": "או<PERSON><PERSON><PERSON><PERSON><PERSON>", "Off": "כב<PERSON>י", "Okay, Let's Go!": "בסדר, בואו נתחיל!", "OLED Dark": "OLE<PERSON> כהה", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API disabled": "Ollama API מושבת", "Ollama API settings updated": "", "Ollama Version": "גר<PERSON><PERSON>", "On": "פועל", "Only alphanumeric characters and hyphens are allowed": "", "Only alphanumeric characters and hyphens are allowed in the command string.": "רק תווים אלפאנומריים ומקפים מותרים במחרוזת הפקודה.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "", "Only select users and groups with permission can access": "", "Oops! Looks like the URL is invalid. Please double-check and try again.": "אופס! נראה שהכתובת URL אינה תקינה. אנא בדוק שוב ונסה שנית.", "Oops! There are files still uploading. Please wait for the upload to complete.": "", "Oops! There was an error in the previous response.": "", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "אופס! אתה משתמש בשיטה לא נתמכת (רק חזית). אנא שרת את ממשק המשתמש האינטרנטי מהשרת האחורי.", "Open in full screen": "", "Open new chat": "פתח צ'אט חדש", "Open WebUI uses faster-whisper internally.": "", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "", "OpenAI": "OpenAI", "OpenAI API": "API של OpenAI", "OpenAI API Config": "תצורת API של OpenAI", "OpenAI API Key is required.": "נדרש מפתח API של OpenAI.", "OpenAI API settings updated": "", "OpenAI URL/Key required.": "נדרשת כתובת URL/מפתח של OpenAI.", "or": "או", "Organize your users": "", "OUTPUT": "", "Output format": "", "Overview": "", "page": "", "Password": "סיסמה", "Paste Large Text as File": "", "PDF document (.pdf)": "מסמך PDF (.pdf)", "PDF Extract Images (OCR)": "חילוץ תמונות מ-PDF (OCR)", "pending": "ממתין", "Permission denied when accessing media devices": "", "Permission denied when accessing microphone": "", "Permission denied when accessing microphone: {{error}}": "ההרשאה נדחתה בעת גישה למיקרופון: {{error}}", "Permissions": "", "Personalization": "תאור", "Pin": "", "Pinned": "", "Pioneer insights": "", "Pipeline deleted successfully": "", "Pipeline downloaded successfully": "", "Pipelines": "צינורות", "Pipelines Not Detected": "", "Pipelines Valves": "צינורות שסתומים", "Plain text (.txt)": "טקסט פשוט (.txt)", "Playground": "אזור משחקים", "Please carefully review the following warnings:": "", "Please enter a prompt": "", "Please fill in all fields.": "", "Please select a model first.": "", "Port": "", "Prefix ID": "", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "", "Previous 30 days": "30 הימים הקודמים", "Previous 7 days": "7 הימים הקודמים", "Profile Image": "תמונת פרופיל", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "פקודה (ל<PERSON><PERSON><PERSON>, ספר לי עובדה מעניינת על האימפריה הרומית)", "Prompt Content": "ת<PERSON><PERSON><PERSON> הפקודה", "Prompt created successfully": "", "Prompt suggestions": "הצעות לפקודות", "Prompt updated successfully": "", "Prompts": "פקודות", "Prompts Access": "", "Provide any specific details": "", "Proxy URL": "", "Pull \"{{searchValue}}\" from Ollama.com": "משוך \"{{searchValue}}\" מ-Ollama.com", "Pull a model from Ollama.com": "משוך מודל מ-Ollama.com", "Query Generation Prompt": "", "Query Params": "פרמטרי שאילתה", "RAG Template": "תבנית RAG", "Rating": "", "Re-rank models by topic similarity": "", "Read Aloud": "קרא בקול", "Record voice": "הקל<PERSON> קול", "Redirecting you to OpenWebUI Community": "מפנה אותך לקהילת OpenWebUI", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative. (Default: 40)": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "", "References from": "", "Refresh Token Expiration": "", "Regenerate": "הפ<PERSON> מחדש", "Release Notes": "הערות שחרור", "Relevance": "", "Remove": "הסר", "Remove Model": "ה<PERSON>ר מודל", "Rename": "שנה שם", "Reorder Models": "", "Repeat Last N": "חזור על ה-N האחרונים", "Reply in Thread": "", "Request Mode": "מצב בק<PERSON>ה", "Reranking Model": "מודל דירוג מחדש", "Reranking model disabled": "מודל דירוג מחדש מושבת", "Reranking model set to \"{{reranking_model}}\"": "מודל דירוג מחדש הוגדר ל-\"{{reranking_model}}\"", "Reset": "", "Reset All Models": "", "Reset Upload Directory": "", "Reset Vector Storage/Knowledge": "", "Reset view": "", "Response generation stopped": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "", "Response splitting": "", "Result": "", "Retrieval Query Generation": "", "Rich Text Input for Chat": "", "RK": "", "Role": "תפקיד", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "", "Running": "", "Save": "שמור", "Save & Create": "ש<PERSON><PERSON><PERSON> וצור", "Save & Update": "שמו<PERSON> ועדכן", "Save As Copy": "", "Save Tag": "", "Saved": "", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "שמירת יומני צ'אט ישירות באחסון הדפדפן שלך אינה נתמכת יותר. אנא הקדש רגע להוריד ולמחוק את יומני הצ'אט שלך על ידי לחיצה על הכפתור למטה. אל דאגה, באפשרותך לייבא מחדש בקלות את יומני הצ'אט שלך לשרת האחורי דרך", "Scroll to bottom when switching between branches": "", "Search": "ח<PERSON><PERSON>", "Search a model": "<PERSON><PERSON><PERSON> מודל", "Search Base": "", "Search Chats": "חיפוש צ'אטים", "Search Collection": "", "Search Filters": "", "search for tags": "", "Search Functions": "", "Search Knowledge": "", "Search Models": "חיפוש מודלים", "Search options": "", "Search Prompts": "<PERSON><PERSON><PERSON> פקודות", "Search Result Count": "ספירת תוצאות חיפוש", "Search the web": "", "Search Tools": "", "Search users": "", "SearchApi API Key": "", "SearchApi Engine": "", "Searched {{count}} sites": "", "Searching \"{{searchQuery}}\"": "", "Searching Knowledge for \"{{searchQuery}}\"": "", "Searxng Query URL": "כתובת URL של שאילתת Searxng", "See readme.md for instructions": "ראה את readme.md להוראות", "See what's new": "ראה מה חדש", "Seed": "זרע", "Select a base model": "בחירת מודל בסיס", "Select a engine": "", "Select a function": "", "Select a group": "", "Select a model": "<PERSON><PERSON><PERSON> מודל", "Select a pipeline": "<PERSON><PERSON><PERSON> <PERSON>ו צינור", "Select a pipeline url": "בחר כתובת URL של קו צינור", "Select a tool": "", "Select Engine": "", "Select Knowledge": "", "Select model": "<PERSON><PERSON><PERSON> מודל", "Select only one model to call": "", "Selected model(s) do not support image inputs": "דגמים נבחרים אינם תומכים בקלט תמונה", "Semantic distance to query": "", "Send": "שלח", "Send a message": "", "Send a Message": "שלח הודעה", "Send message": "שלח הודעה", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "", "September": "ספט<PERSON><PERSON>ר", "Serper API Key": "מפתח Serper API", "Serply API Key": "", "Serpstack API Key": "מפתח API של Serpstack", "Server connection verified": "החיבור לשרת אומת", "Set as default": "הגדר כברירת מחדל", "Set CFG Scale": "", "Set Default Model": "הגדר מודל ברירת מחדל", "Set embedding model": "", "Set embedding model (e.g. {{model}})": "הגדר מודל הטמעה (למשל {{model}})", "Set Image Size": "הגדר גודל תמונה", "Set reranking model (e.g. {{model}})": "הגדר מודל דירוג מחדש (למשל {{model}})", "Set Sampler": "", "Set Scheduler": "", "Set Steps": "הגדר שלבים", "Set Task Model": "הגדרת מודל משימה", "Set the number of GPU devices used for computation. This option controls how many GPU devices (if available) are used to process incoming requests. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "", "Set Voice": "ה<PERSON><PERSON><PERSON> קול", "Set whisper model": "", "Sets how far back for the model to look back to prevent repetition. (Default: 64, 0 = disabled, -1 = num_ctx)": "", "Sets how strongly to penalize repetitions. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. (Default: 1.1)": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt. (Default: random)": "", "Sets the size of the context window used to generate the next token. (Default: 2048)": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "", "Settings": "הגדרות", "Settings saved successfully!": "ההגדרות נשמרו בהצלחה!", "Share": "שתף", "Share Chat": "שתף צ'אט", "Share to OpenWebUI Community": "שתף לקהילת OpenWebUI", "Show": "הצג", "Show \"What's New\" modal on login": "", "Show Admin Details in Account Pending Overlay": "", "Show shortcuts": "הצג קיצורי דרך", "Show your support!": "", "Sign in": "הירשם", "Sign in to {{WEBUI_NAME}}": "", "Sign in to {{WEBUI_NAME}} with LDAP": "", "Sign Out": "התנתקות", "Sign up": "הרשמה", "Sign up to {{WEBUI_NAME}}": "", "Signing in to {{WEBUI_NAME}}": "", "sk-1234": "", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Speech Playback Speed": "", "Speech recognition error: {{error}}": "שגי<PERSON>ת תחקור שמע: {{error}}", "Speech-to-Text Engine": "מנוע תחקור שמע", "Stop": "", "Stop Sequence": "סידור עצירה", "Stream Chat Response": "", "STT Model": "", "STT Settings": "הגדרות חקירה של TTS", "Success": "הצלחה", "Successfully updated.": "עדכון הצלחה.", "Suggested prompts to get you started": "", "Support": "", "Support this plugin:": "", "Sync directory": "", "System": "מערכת", "System Instructions": "", "System Prompt": "תגובת מערכת", "Tags Generation": "", "Tags Generation Prompt": "", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting. (default: 1)": "", "Tap to interrupt": "", "Tavily API Key": "", "Temperature": "טמפרטורה", "Template": "תבנית", "Temporary Chat": "", "Text Splitter": "", "Text-to-Speech Engine": "מנוע טקסט לדיבור", "Tfs Z": "Tfs Z", "Thanks for your feedback!": "תודה על המשוב שלך!", "The Application Account DN you bind with for search": "", "The base to search for users": "", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.  (Default: 512)": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "", "The LDAP attribute that maps to the username that users use to sign in.": "", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "ציון צריך להיות ערך בין 0.0 (0%) ל-1.0 (100%)", "The temperature of the model. Increasing the temperature will make the model answer more creatively. (Default: 0.8)": "", "Theme": "נושא", "Thinking...": "", "This action cannot be undone. Do you wish to continue?": "", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "פעולה זו מבטיחה שהשיחות בעלות הערך שלך יישמרו באופן מאובטח במסד הנתונים העורפי שלך. תודה!", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics. (Default: 24)": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.  (Default: 128)": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "", "This response was generated by \"{{model}}\"": "", "This will delete": "", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "", "This will delete all models including custom models": "", "This will delete all models including custom models and cannot be undone.": "", "This will reset the knowledge base and sync all files. Do you wish to continue?": "", "Tika": "", "Tika Server URL required.": "", "Tiktoken": "", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "טיפ: עד<PERSON><PERSON> חריצים משתנים מרובים ברציפות על-ידי לחיצה על מקש Tab בקלט הצ'אט לאחר כל החלפה.", "Title": "שם", "Title (e.g. Tell me a fun fact)": "שם (לדוגמה: תרגום)", "Title Auto-Generation": "יצירת שם אוטומטית", "Title cannot be an empty string.": "שם לא יכול להיות מחרוזת ריקה.", "Title Generation Prompt": "שאלה ליצירת שם", "TLS": "", "To access the available model names for downloading,": "כדי לגשת לשמות הדגמים הזמינים להורדה,", "To access the GGUF models available for downloading,": "כדי לגשת לדגמי GGUF הזמינים להורדה,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "", "To learn more about available endpoints, visit our documentation.": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "", "To select actions here, add them to the \"Functions\" workspace first.": "", "To select filters here, add them to the \"Functions\" workspace first.": "", "To select toolkits here, add them to the \"Tools\" workspace first.": "", "Toast notifications for new updates": "", "Today": "היום", "Toggle settings": "החלפת מצב של הגדרות", "Toggle sidebar": "החלפת מצב של סרגל הצד", "Token": "", "Tokens To Keep On Context Refresh (num_keep)": "", "Tool created successfully": "", "Tool deleted successfully": "", "Tool Description": "", "Tool ID": "", "Tool imported successfully": "", "Tool Name": "", "Tool updated successfully": "", "Tools": "", "Tools Access": "", "Tools are a function calling system with arbitrary code execution": "", "Tools have a function calling system that allows arbitrary code execution": "", "Tools have a function calling system that allows arbitrary code execution.": "", "Top K": "Top K", "Top P": "Top P", "Transformers": "", "Trouble accessing Ollama?": "קשה לגשת לOllama?", "TTS Model": "", "TTS Settings": "הגדרות TTS", "TTS Voice": "", "Type": "סוג", "Type Hugging Face Resolve (Download) URL": "הקלד כתובת URL של פתרון פנים מחבק (הורד)", "Uh-oh! There was an issue with the response.": "", "UI": "", "Unarchive All": "", "Unarchive All Archived Chats": "", "Unarchive Chat": "", "Unlock mysteries": "", "Unpin": "", "Unravel secrets": "", "Untagged": "", "Update": "", "Update and Copy Link": "ע<PERSON><PERSON><PERSON> ושכ<PERSON>ל קישור", "Update for the latest features and improvements.": "", "Update password": "עד<PERSON><PERSON> סיסמה", "Updated": "", "Updated at": "", "Updated At": "", "Upload": "", "Upload a GGUF model": "העלה מודל GGUF", "Upload directory": "", "Upload files": "", "Upload Files": "העלאת קבצים", "Upload Pipeline": "", "Upload Progress": "תקדמות העלאה", "URL": "", "URL Mode": "מצב URL", "Use '#' in the prompt input to load and include your knowledge.": "", "Use Gravatar": "שימוש ב Gravatar", "Use groups to group your users and assign permissions.": "", "Use Initials": "שימוש ב initials", "use_mlock (Ollama)": "use_mlock (אולמה)", "use_mmap (Ollama)": "use_mmap (Ollama)", "user": "משת<PERSON>ש", "User": "", "User location successfully retrieved.": "", "Username": "", "Users": "משתמשים", "Using the default arena model with all models. Click the plus button to add custom models.": "", "Utilize": "שימוש", "Valid time units:": "יחידות זמן תקינות:", "Valves": "", "Valves updated": "", "Valves updated successfully": "", "variable": "משתנה", "variable to have them replaced with clipboard content.": "משתנה להחליפו ב- clipboard תוכן.", "Version": "גרסה", "Version {{selectedVersion}} of {{totalVersions}}": "", "Very bad": "", "View Replies": "", "Visibility": "", "Voice": "", "Voice Input": "", "Warning": "אזהרה", "Warning:": "", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "אזהרה: אם תעדכן או תשנה את מודל ההטבעה שלך, יהיה עליך לייבא מחדש את כל המסמכים.", "Web": "רשת", "Web API": "", "Web Loader Settings": "הגדרות טעינת אתר", "Web Search": "<PERSON><PERSON><PERSON><PERSON><PERSON> באינטרנט", "Web Search Engine": "מנוע חיפוש באינטרנט", "Web Search Query Generation": "", "Webhook URL": "URL Webhook", "WebUI Settings": "הגדרות WebUI", "WebUI URL": "", "WebUI will make requests to \"{{url}}/api/chat\"": "", "WebUI will make requests to \"{{url}}/chat/completions\"": "", "What are you trying to achieve?": "", "What are you working on?": "", "What didn't you like about this response?": "", "What’s New in": "מה חדש ב", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "", "wherever you are": "", "Whisper (Local)": "", "Widescreen Mode": "", "Won": "", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text. (Default: 0.9)": "", "Workspace": "סביבה", "Workspace Permissions": "", "Write a prompt suggestion (e.g. Who are you?)": "כתוב הצעה מהירה (למשל, מי אתה?)", "Write a summary in 50 words that summarizes [topic or keyword].": "כתוב סיכום ב-50 מילים שמסכם [נושא או מילת מפתח].", "Write something...": "", "Write your model template content here": "", "Yesterday": "אתמול", "You": "אתה", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "", "You cannot upload an empty file.": "", "You have no archived conversations.": "אין לך שיחות בארכיון.", "You have shared this chat": "שיתפת את השיחה הזו", "You're a helpful assistant.": "אתה עוזר מועיל.", "Your account status is currently pending activation.": "", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "", "Youtube": "Youtube", "Youtube Loader Settings": "הגדרות Youtube Loader"}