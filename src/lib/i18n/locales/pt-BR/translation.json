{"-1 for no limit, or a positive integer for a specific limit": "", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' ou '-1' para sem expiração.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(por exemplo, `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(por exemplo, `sh webui.sh --api`)", "(latest)": "(último)", "{{ models }}": "{{ models }}", "{{COUNT}} Replies": "", "{{user}}'s Chats": "Chats de {{user}}", "{{webUIName}} Backend Required": "Backend {{webUIName}} necess<PERSON>rio", "*Prompt node ID(s) are required for image generation": "*Prompt node ID(s) são obrigatórios para gerar imagens", "A new version (v{{LATEST_VERSION}}) is now available.": "Um nova versão (v{{LATEST_VERSION}}) está disponível.", "A task model is used when performing tasks such as generating titles for chats and web search queries": "Um modelo de tarefa é usado ao realizar tarefas como gerar títulos para chats e consultas de pesquisa na web", "a user": "um usuário", "About": "Sobre", "Access": "Ace<PERSON>", "Access Control": "Controle de Acesso", "Accessible to all users": "Accessível para todos os usuários", "Account": "Conta", "Account Activation Pending": "Ativação da Conta Pendente", "Actions": "Ações", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "Ativar esse comando no chat digitando \"/{{COMMAND}}\"", "Active Users": "Usuários Ativos", "Add": "<PERSON><PERSON><PERSON><PERSON>", "Add a model ID": "Adicione um ID de modelo", "Add a short description about what this model does": "Adicione uma descrição curta sobre o que este modelo faz", "Add a tag": "Adici<PERSON>r uma tag", "Add Arena Model": "Adicionar Modelo Arena", "Add Connection": "<PERSON><PERSON><PERSON><PERSON>", "Add Content": "<PERSON><PERSON><PERSON><PERSON>", "Add content here": "Ad<PERSON><PERSON><PERSON> con<PERSON><PERSON><PERSON> aqui", "Add custom prompt": "Adicionar prompt personalizado", "Add Files": "<PERSON><PERSON><PERSON><PERSON>", "Add Group": "Adicionar Grupo", "Add Memory": "<PERSON><PERSON><PERSON><PERSON>", "Add Model": "<PERSON><PERSON><PERSON><PERSON>", "Add Reaction": "", "Add Tag": "Adicionar <PERSON>", "Add Tags": "Adicionar Tags", "Add text content": "Adicionar con<PERSON><PERSON><PERSON> de texto", "Add User": "<PERSON><PERSON><PERSON><PERSON>", "Add User Group": "Adicionar grupo de usuários", "Adjusting these settings will apply changes universally to all users.": "Ajustar essas configurações aplicará mudanças para todos os usuários.", "admin": "admin", "Admin": "Admin", "Admin Panel": "Painel do Admin", "Admin Settings": "Configurações do Admin", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "Os admins têm acesso a todas as ferramentas o tempo todo; os usuários precisam de ferramentas atribuídas, por modelo, no workspace.", "Advanced Parameters": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Advanced Params": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "All Documents": "Todos os Documentos", "All models deleted successfully": "Todos os modelos foram excluídos com sucesso", "Allow Chat Delete": "Permit<PERSON>", "Allow Chat Deletion": "Permit<PERSON>", "Allow Chat Edit": "Permitir <PERSON>", "Allow File Upload": "<PERSON><PERSON><PERSON>", "Allow non-local voices": "<PERSON><PERSON><PERSON> vozes não locais", "Allow Temporary Chat": "<PERSON><PERSON><PERSON>", "Allow User Location": "Permitir Localização do Usuário", "Allow Voice Interruption in Call": "Permitir Interrupção de Voz na Chamada", "Allowed Endpoints": "", "Already have an account?": "Já tem uma conta?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out. (Default: 0.0)": "Alternativa ao 'top_p', e visa garantir um equilíbrio entre qualidade e variedade. O parâmetro 'p' representa a probabilidade mínima para que um token seja considerado, em relação à probabilidade do token mais provável. Por exemplo, com 'p=0.05' e o token mais provável com probabilidade de '0.9', as predições com valor inferior a '0.045' são filtrados. (Default: 0.0)", "an assistant": "um assistente", "and": "e", "and {{COUNT}} more": "e mais {{COUNT}}", "and create a new shared link.": "e criar um novo link compartilhado.", "API Base URL": "URL Base da API", "API Key": "Chave API", "API Key created.": "Chave API criada.", "API Key Endpoint Restrictions": "", "API keys": "Chaves API", "Application DN": "DN da Aplicação", "Application DN Password": "Senha da aplicação DN", "applies to all users with the \"user\" role": "Aplicar para todos com permissão de \"usuário\"", "April": "Abril", "Archive": "<PERSON><PERSON><PERSON><PERSON>", "Archive All Chats": "<PERSON><PERSON><PERSON><PERSON>", "Archived Chats": "Chats Arquivados", "archived-chat-export": "", "Are you sure you want to delete this channel?": "", "Are you sure you want to delete this message?": "", "Are you sure you want to unarchive all archived chats?": "Você tem certeza que deseja desarquivar todos os chats arquivados?", "Are you sure?": "Você tem certeza?", "Arena Models": "Arena de Modelos", "Artifacts": "Arte<PERSON><PERSON>", "Ask a question": "Faça uma pergunta", "Assistant": "<PERSON><PERSON><PERSON>", "Attach file": "Anexar arquivo", "Attribute for Username": "Atribuir para nome de usuário", "Audio": "<PERSON><PERSON><PERSON>", "August": "Agosto", "Authenticate": "Autenticar", "Auto-Copy Response to Clipboard": "Cópia Automática da Resposta para a Área de Transferência", "Auto-playback response": "Resposta de reprodução automática", "Autocomplete Generation": "", "Autocomplete Generation Input Max Length": "", "Automatic1111": "Automatic1111", "AUTOMATIC1111 Api Auth String": "String de Autenticação da API AUTOMATIC1111", "AUTOMATIC1111 Base URL": "URL Base AUTOMATIC1111", "AUTOMATIC1111 Base URL is required.": "URL Base AUTOMATIC1111 é necessária.", "Available list": "Lista disponível", "available!": "disponível!", "Azure AI Speech": "", "Azure Region": "", "Back": "Voltar", "Bad": "", "Bad Response": "<PERSON>sp<PERSON><PERSON>", "Banners": "Banners", "Base Model (From)": "Modelo Base (De)", "Batch Size (num_batch)": "<PERSON><PERSON><PERSON> (num_batch)", "before": "antes", "Beta": "", "BETA": "", "Bing Search V7 Endpoint": "", "Bing Search V7 Subscription Key": "", "Brave Search API Key": "Chave API do Brave Search", "By {{name}}": "Por {{name}}", "Bypass SSL verification for Websites": "Ignorar verificação SSL para Sites", "Call": "<PERSON><PERSON>", "Call feature is not supported when using Web STT engine": "O recurso de chamada não é suportado ao usar o mecanismo Web STT", "Camera": "Câmera", "Cancel": "<PERSON><PERSON><PERSON>", "Capabilities": "Capacidades", "Capture": "", "Certificate Path": "Caminho do Certificado", "Change Password": "<PERSON><PERSON>", "Channel Name": "", "Channels": "", "Character": "<PERSON><PERSON>", "Character limit for autocomplete generation input": "", "Chart new frontiers": "Trace novas fronteiras", "Chat": "Cha<PERSON>", "Chat Background Image": "Imagem de Fundo do Chat", "Chat Bubble UI": "<PERSON><PERSON> de <PERSON>ha <PERSON>", "Chat Controls": "<PERSON>es de Chat", "Chat direction": "Direção do Chat", "Chat Overview": "Visão Geral do Chat", "Chat Permissions": "Permissões de Chat", "Chat Tags Auto-Generation": "Tags de Chat Geradas Automaticamente", "Chats": "Chats", "Check Again": "Verificar Novamente", "Check for updates": "Verificar atualizações", "Checking for updates...": "Verificando atualizações...", "Choose a model before saving...": "Escolha um modelo antes de salvar...", "Chunk Overlap": "Sobreposição de Chunk", "Chunk Params": "<PERSON>râ<PERSON><PERSON>", "Chunk Size": "Tamanho do <PERSON>k", "Ciphers": "Cifras", "Citation": "Citação", "Clear memory": "<PERSON><PERSON>", "click here": "Clique aqui", "Click here for filter guides.": "Clique aqui para obter instruções de filtros.", "Click here for help.": "Clique aqui para obter ajuda.", "Click here to": "Clique aqui para", "Click here to download user import template file.": "Clique aqui para baixar o arquivo de modelo de importação de usuários.", "Click here to learn more about faster-whisper and see the available models.": "Clique aqui para aprender mais sobre Whisper e ver os modelos disponíveis.", "Click here to select": "Clique aqui para enviar", "Click here to select a csv file.": "Clique aqui para enviar um arquivo csv.", "Click here to select a py file.": "Clique aqui para enviar um arquivo python.", "Click here to upload a workflow.json file.": "Clique aqui para enviar um arquivo workflow.json.", "click here.": "clique aqui.", "Click on the user role button to change a user's role.": "Clique no botão de função do usuário para alterar a função de um usuário.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "Permissão de escrita na área de transferência negada. Verifique as configurações do seu navegador para conceder o acesso necessário.", "Clone": "Clonar", "Close": "<PERSON><PERSON><PERSON>", "Code execution": "Execução de código", "Code formatted successfully": "Código formatado com sucesso", "Collection": "Coleção", "Color": "Cor", "ComfyUI": "ComfyUI", "ComfyUI API Key": "", "ComfyUI Base URL": "URL Base do ComfyUI", "ComfyUI Base URL is required.": "URL Base do ComfyUI é necessária.", "ComfyUI Workflow": "", "ComfyUI Workflow Nodes": "", "Command": "Comand<PERSON>", "Completions": "<PERSON><PERSON>lus<PERSON><PERSON>", "Concurrent Requests": "Solicitações Concomitantes", "Configure": "Configurar", "Configure Models": "", "Confirm": "Confirmar", "Confirm Password": "Confirmar <PERSON>", "Confirm your action": "Confirme sua ação", "Confirm your new password": "", "Connections": "Conexões", "Contact Admin for WebUI Access": "Contate o Admin para Acesso ao WebUI", "Content": "<PERSON><PERSON><PERSON><PERSON>", "Content Extraction": "Extração de Conteúdo", "Context Length": "<PERSON><PERSON><PERSON>", "Continue Response": "<PERSON><PERSON><PERSON><PERSON>", "Continue with {{provider}}": "Continuar com {{provider}}", "Continue with Email": "Continuar com Email", "Continue with LDAP": "Continuar com LDAP", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "Controlar como o texto do mensagem é dividido para solicitações TTS. 'Pontuação' dividida em frases, 'parágrafos' divide em parágrafos e 'não' mantém a mensagem como uma cadeia de caracteres.", "Controls": "Controles", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text. (Default: 5.0)": "Controlar o equilibrio entre a coerencia e a diversidade da saída. Um valor mais baixo fará com que o texto seja mais focado e coerente. (Padrão: 5.0)", "Copied": "Copiado", "Copied shared chat URL to clipboard!": "URL de chat compartilhado copiado para a área de transferência!", "Copied to clipboard": "Copiado para a área de transferência", "Copy": "Copiar", "Copy last code block": "Copiar último bloco de código", "Copy last response": "Copiar última resposta", "Copy Link": "Copiar Link", "Copy to clipboard": "Copiar para a área de transferência", "Copying to clipboard was successful!": "Cópia para a área de transferência bem-sucedida!", "Create": "<PERSON><PERSON><PERSON>", "Create a knowledge base": "Criar uma base de conhecimento", "Create a model": "<PERSON><PERSON>r um modelo", "Create Account": "<PERSON><PERSON><PERSON>", "Create Admin Account": "<PERSON><PERSON><PERSON>", "Create Channel": "", "Create Group": "Criar Grupo", "Create Knowledge": "<PERSON><PERSON><PERSON>", "Create new key": "Criar nova chave", "Create new secret key": "Criar nova chave secreta", "Created at": "C<PERSON><PERSON> em", "Created At": "Criado Em", "Created by": "<PERSON><PERSON><PERSON> por", "CSV Import": "Importação CSV", "Current Model": "<PERSON><PERSON>", "Current Password": "<PERSON><PERSON>", "Custom": "Personalizado", "Dark": "Escuro", "Database": "Banco de Dados", "December": "Dezembro", "Default": "Padrão", "Default (Open AI)": "<PERSON><PERSON><PERSON> (Open AI)", "Default (SentenceTransformers)": "Padrão (SentenceTransformers)", "Default Model": "<PERSON><PERSON>", "Default model updated": "<PERSON><PERSON> atualizado", "Default Models": "", "Default permissions": "Permissões padr<PERSON>", "Default permissions updated successfully": "Permissões padrão atualizadas com sucesso", "Default Prompt Suggestions": "Sugestões de Prompt Padrão", "Default to 389 or 636 if TLS is enabled": "", "Default to ALL": "Padrão para TODOS", "Default User Role": "Padrão para novos usuários", "Delete": "Excluir", "Delete a model": "Excluir um modelo", "Delete All Chats": "Excluir Todos os Chats", "Delete All Models": "Excluir Todos os Modelos", "Delete chat": "Excluir chat", "Delete Chat": "Excluir <PERSON>", "Delete chat?": "Excluir chat?", "Delete folder?": "Excluir pasta?", "Delete function?": "Excluir função?", "Delete Message": "", "Delete prompt?": "Excluir prompt?", "delete this link": "Excluir este link", "Delete tool?": "Excluir ferramenta?", "Delete User": "Excluir <PERSON>", "Deleted {{deleteModelTag}}": "Excluído {{deleteModelTag}}", "Deleted {{name}}": "<PERSON>clu<PERSON><PERSON> {{name}}", "Deleted User": "<PERSON><PERSON><PERSON><PERSON>", "Describe your knowledge base and objectives": "Descreva sua base de conhecimento e objetivos", "Description": "Descrição", "Disabled": "Desativado", "Discover a function": "Descubra uma função", "Discover a model": "Descubra um modelo", "Discover a prompt": "Descubra um prompt", "Discover a tool": "Descubra uma ferramenta", "Discover wonders": "Descobrir ma<PERSON>", "Discover, download, and explore custom functions": "Descubra, baixe e explore funções personalizadas", "Discover, download, and explore custom prompts": "<PERSON><PERSON><PERSON>, baixe e explore prompts personalizados", "Discover, download, and explore custom tools": "Descubra, baixe e explore ferramentas personalizadas", "Discover, download, and explore model presets": "Descubra, baixe e explore predefinições de modelos", "Dismissible": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Display": "<PERSON><PERSON><PERSON>", "Display Emoji in Call": "<PERSON><PERSON><PERSON>", "Display the username instead of You in the Chat": "Exibir o nome de usuário em vez de Você no Chat", "Displays citations in the response": "Exibir citações na resposta", "Dive into knowledge": "Explorar base de conhecimento", "Do not install functions from sources you do not fully trust.": "Não instale funções de fontes que você não confia totalmente.", "Do not install tools from sources you do not fully trust.": "Não instale ferramentas de fontes que você não confia totalmente.", "Document": "Documento", "Documentation": "Documentação", "Documents": "Documentos", "does not make any external connections, and your data stays securely on your locally hosted server.": "não faz nenhuma conexão externa, e seus dados permanecem seguros no seu servidor local.", "Don't have an account?": "Não tem uma conta?", "don't install random functions from sources you don't trust.": "não instale funções aleatórias de fontes que você não confia.", "don't install random tools from sources you don't trust.": "não instale ferramentas aleatórias de fontes que você não confia.", "Done": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Download": "Baixar", "Download canceled": "Download cancelado", "Download Database": "Baixar Banco de Dados", "Drag and drop a file to upload or select a file to view": "Arraste e solte um arquivo para enviar ou selecione um arquivo para visualizar", "Draw": "Empate", "Drop any files here to add to the conversation": "Solte qualquer arquivo aqui para adicionar à conversa", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "por exemplo, '30s', '10m'. Unidades de tempo válidas são 's', 'm', 'h'.", "e.g. A filter to remove profanity from text": "Exemplo: Um filtro para remover palavrões do texto", "e.g. My Filter": "Exemplo: <PERSON><PERSON>", "e.g. My Tools": "Exemplo: Minhas Ferramentas", "e.g. my_filter": "Exemplo: my_filter", "e.g. my_tools": "Exemplo: my_tools", "e.g. Tools for performing various operations": "Exemplo: Ferramentas para executar operações diversas", "Edit": "<PERSON><PERSON>", "Edit Arena Model": "Editar Arena de Modelos", "Edit Channel": "", "Edit Connection": "<PERSON><PERSON>", "Edit Default Permissions": "<PERSON>ar <PERSON>", "Edit Memory": "<PERSON><PERSON>", "Edit User": "<PERSON><PERSON>", "Edit User Group": "Editar Grupo de Usuários", "ElevenLabs": "", "Email": "Email", "Embark on adventures": "Embarque em aventuras", "Embedding Batch Size": "Tamanho do Lote de Embedding", "Embedding Model": "Modelo de Embedding", "Embedding Model Engine": "Motor do Modelo de Embedding", "Embedding model set to \"{{embedding_model}}\"": "Modelo de embedding definido para \"{{embedding_model}}\"", "Enable API Key": "", "Enable autocomplete generation for chat messages": "", "Enable Community Sharing": "Ativar Compartilhamento com a Comunidade", "Enable Google Drive": "", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "Habilite o bloqueio de memória (mlock) para evitar que os dados do modelo sejam transferidos da RAM para a área de troca (swap). Essa opção bloqueia o conjunto de páginas em uso pelo modelo na RAM, garantindo que elas não sejam transferidas para o disco. Isso pode ajudar a manter o desempenho, evitando falhas de página e garantindo acesso rápido aos dados.", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "Habilite o mapeamento de memória (mmap) para carregar dados do modelo. Esta opção permite que o sistema use o armazenamento em disco como uma extensão da RAM, tratando os arquivos do disco como se estivessem na RAM. Isso pode melhorar o desempenho do modelo, permitindo acesso mais rápido aos dados. No entanto, pode não funcionar corretamente com todos os sistemas e consumir uma quantidade significativa de espaço em disco.", "Enable Message Rating": "Ativar Avaliação de Mensagens", "Enable Mirostat sampling for controlling perplexity. (Default: 0, 0 = Disabled, 1 = Mirostat, 2 = Mirostat 2.0)": "Habilite a amostragem Mirostat para controlar a perplexidade. (Padrão: 0, 0 = Desativado, 1 = Mirostat, 2 = Mirostat 2.0)", "Enable New Sign Ups": "Ativar Novos Cadastros", "Enable Web Search": "Ativar Pesquisa na Web", "Enabled": "<PERSON><PERSON>do", "Engine": "Motor", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "Certifique-se de que seu arquivo CSV inclua 4 colunas nesta ordem: Nome, Email, Senha, Função.", "Enter {{role}} message here": "Digite a mensagem de {{role}} aqui", "Enter a detail about yourself for your LLMs to recall": "Digite um detalhe sobre você para seus LLMs lembrarem", "Enter api auth string (e.g. username:password)": "Digite a string de autenticação da API (por exemplo, username:password)", "Enter Application DN": "Digite o DN da Aplicação", "Enter Application DN Password": "Digite a Senha do DN da Aplicação", "Enter Bing Search V7 Endpoint": "Digite o Endpoint do Bing Search V7", "Enter Bing Search V7 Subscription Key": "Digite a Chave de Assinatura do Bing Search V7", "Enter Brave Search API Key": "Digite a Chave API do Brave Search", "Enter certificate path": "Digite o caminho do certificado", "Enter CFG Scale (e.g. 7.0)": "Digite a escala de CFG (por exemplo, 7.0)", "Enter Chunk Overlap": "Digite a Sobreposição de Chunk", "Enter Chunk Size": "Digite o Tamanho do Chunk", "Enter description": "Digite a descrição", "Enter Github Raw URL": "Digite a URL bruta do Github", "Enter Google PSE API Key": "Digite a Chave API do Google PSE", "Enter Google PSE Engine Id": "Digite o ID do Motor do Google PSE", "Enter Image Size (e.g. 512x512)": "<PERSON><PERSON><PERSON> o Tam<PERSON>ho da <PERSON>m (por exemplo, 512x512)", "Enter Jina API Key": "Digite a Chave API Jina", "Enter Kagi Search API Key": "", "Enter language codes": "Digite os códigos de idioma", "Enter Model ID": "Digite o ID do modelo", "Enter model tag (e.g. {{modelTag}})": "Digite a tag do modelo (por exemplo, {{modelTag}})", "Enter Mojeek Search API Key": "Digite a Chave API do Mojeek Search", "Enter name or email": "", "Enter Number of Steps (e.g. 50)": "Digite o Número de Passos (por exemplo, 50)", "Enter proxy URL (e.g. **************************:port)": "", "Enter Sampler (e.g. Euler a)": "<PERSON><PERSON><PERSON> o Sampler (por exemplo, Euler a)", "Enter Scheduler (e.g. Karras)": "Digite o Agendador (por exemplo, Karras)", "Enter Score": "Digite a Pontuação", "Enter SearchApi API Key": "Digite a Chave API do SearchApi", "Enter SearchApi Engine": "Digite o Motor do SearchApi", "Enter Searxng Query URL": "Digite a URL de Consulta do Searxng", "Enter Seed": "", "Enter Serper API Key": "Digite a Chave API do Serper", "Enter Serply API Key": "Digite a Chave API do Serply", "Enter Serpstack API Key": "Digite a Chave API do Serpstack", "Enter server host": "Digite o host do servidor", "Enter server label": "Digite o label do servidor", "Enter server port": "Digite a porta do servidor", "Enter stop sequence": "Digite a sequência de parada", "Enter system prompt": "Digite o prompt do sistema", "Enter Tavily API Key": "Digite a Chave API do Tavily", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter Tika Server URL": "Digite a URL do Servidor Tika", "Enter Top K": "Digite o Top K", "Enter URL (e.g. http://127.0.0.1:7860/)": "Digite a URL (por exemplo, http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "Digite a URL (por exemplo, http://localhost:11434)", "Enter your current password": "", "Enter Your Email": "<PERSON><PERSON><PERSON>", "Enter Your Full Name": "Digite Seu Nome Completo", "Enter your message": "Digite sua mensagem", "Enter your new password": "", "Enter Your Password": "<PERSON><PERSON><PERSON>", "Enter your prompt": "", "Enter Your Role": "Digite Sua Função", "Enter Your Username": "Digite seu usuário", "Enter your webhook URL": "", "Error": "Erro", "ERROR": "", "Error accessing Google Drive: {{error}}": "", "Error uploading file: {{error}}": "", "Evaluations": "Avaliações", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "Exemplo: (&(objectClass=inetOrgPerson)(uid=%s))", "Example: ALL": "Exemplo: ALL", "Example: ou=users,dc=foo,dc=example": "Exemplo: ou=users,dc=foo,dc=example", "Example: sAMAccountName or uid or userPrincipalName": "Exemplo: sAMAccountName ou uid ou userPrincipalName", "Exclude": "Excluir", "Experimental": "Experimental", "Explore the cosmos": "Explorar o cosmos", "Export": "Exportar", "Export All Archived Chats": "Exportar todos os chats arquivados", "Export All Chats (All Users)": "Exportar Todos os Chats (Todos os Usuários)", "Export chat (.json)": "Exportar chat (.json)", "Export Chats": "Exportar Chats", "Export Config to JSON File": "Exportar Configuração para Arquivo JSON", "Export Functions": "Exportar Funções", "Export Models": "Exportar Modelos", "Export Presets": "Exportar Presets", "Export Prompts": "Exportar Prompts", "Export to CSV": "Exportar para CSV", "Export Tools": "Exportar Ferramentas", "External Models": "Modelos Externos", "Extremely bad": "", "Failed to add file.": "Falha ao adicionar arquivo.", "Failed to create API Key.": "Falha ao criar a Chave API.", "Failed to read clipboard contents": "Falha ao ler o conteúdo da área de transferência", "Failed to save models configuration": "", "Failed to update settings": "<PERSON>al<PERSON> ao atualizar as configuraç<PERSON><PERSON>", "February": "<PERSON><PERSON>", "Feedback History": "Histórico de comentários", "Feedbacks": "Comentários", "File": "Arquivo", "File added successfully.": "Arquivo adicionado com sucesso.", "File content updated successfully.": "Arquivo de conteúdo atualizado com sucesso.", "File Mode": "Modo de Arquivo", "File not found.": "Arquivo não encontrado.", "File removed successfully.": "Arquivo removido com sucesso.", "File size should not exceed {{maxSize}} MB.": "Arquivo não pode exceder {{maxSize}} MB.", "File uploaded successfully": "", "Files": "<PERSON>r<PERSON><PERSON>", "Filter is now globally disabled": "O filtro está agora desativado globalmente", "Filter is now globally enabled": "O filtro está agora ativado globalmente", "Filters": "<PERSON><PERSON><PERSON>", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Falsificação de impressão digital detectada: Não foi possível usar as iniciais como avatar. Usando a imagem de perfil padrão.", "Fluidly stream large external response chunks": "Transmitir fluentemente grandes blocos de respostas externas", "Focus chat input": "Focar entrada de chat", "Folder deleted successfully": "Pasta excluída com sucesso", "Folder name cannot be empty": "Nome da pasta não pode estar vazio", "Folder name cannot be empty.": "Nome da pasta não pode estar vazio.", "Folder name updated successfully": "Nome da pasta atualizado com sucesso", "Forge new paths": "Trilhar novos caminhos", "Form": "<PERSON><PERSON><PERSON><PERSON>", "Format your variables using brackets like this:": "Formate suas variáveis usando colchetes como este:", "Frequency Penalty": "Penalização por Frequência", "Function": "Função", "Function created successfully": "Função criada com sucesso", "Function deleted successfully": "Função excluída com sucesso", "Function Description": "Descrição da Função", "Function ID": "ID da Função", "Function is now globally disabled": "A função está agora desativada globalmente", "Function is now globally enabled": "A função está agora ativada globalmente", "Function Name": "Nome da Função", "Function updated successfully": "Função atualizada com sucesso", "Functions": "Funções", "Functions allow arbitrary code execution": "Funções permitem a execução arbitrária de código", "Functions allow arbitrary code execution.": "Funções permitem a execução arbitrária de código.", "Functions imported successfully": "Funções importadas com sucesso", "General": "G<PERSON>", "General Settings": "Configurações Gerais", "Generate Image": "<PERSON><PERSON><PERSON>", "Generating search query": "Gerando consulta de pesquisa", "Get started": "Iniciar", "Get started with {{WEBUI_NAME}}": "Iniciar com {{WEBUI_NAME}}", "Global": "Global", "Good Response": "Boa Resposta", "Google Drive": "", "Google PSE API Key": "Chave API do Google PSE", "Google PSE Engine Id": "ID do Motor do Google PSE", "Group created successfully": "Grupo criado com sucesso", "Group deleted successfully": "Grupo excluído com sucesso", "Group Description": "Descrição do Grupo", "Group Name": "Nome do Grupo", "Group updated successfully": "Grupo atualizado com sucesso", "Groups": "Grupos", "GSA Chat can make mistakes. Review all responses for accuracy.": "", "h:mm a": "h:mm a", "Haptic Feedback": "", "Harmful or offensive": "", "has no conversations.": "não tem conversas.", "Hello, {{name}}": "<PERSON><PERSON><PERSON>, {{name}}", "Help": "<PERSON><PERSON><PERSON>", "Help us create the best community leaderboard by sharing your feedback history!": "Ajude-nos a criar o melhor ranking da comunidade compartilhando sua historia de comentários!", "Hex Color": "Cor hexadecimal", "Hex Color - Leave empty for default color": "Cor Hexadecimal - Deixe em branco para a cor padrão", "Hide": "Ocultar", "Host": "<PERSON><PERSON><PERSON>", "How can I help you today?": "Como posso ajudar você hoje?", "How would you rate this response?": "Como você avalia essa resposta?", "Hybrid Search": "Pesquisa Híbrida", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "Eu reconheço que li e entendi as implicações da minha ação. Estou ciente dos riscos associados à execução de código arbitrário e verifiquei a confiabilidade da fonte.", "ID": "", "Ignite curiosity": "Desperte a curiosidade", "Image Compression": "", "Image Generation (Experimental)": "Geração de Imagem (Experimental)", "Image Generation Engine": "Motor de Geração de Imagem", "Image Max Compression Size": "", "Image Settings": "Configurações de Imagem", "Images": "Imagens", "Import Chats": "Importar Chats", "Import Config from JSON File": "Importar Configurações de JSON", "Import Functions": "Importar Funções", "Import Models": "Importar Modelos", "Import Presets": "Importar Presets", "Import Prompts": "Importar Prompts", "Import Tools": "Importar Ferramentas", "Include": "Incluir", "Include `--api-auth` flag when running stable-diffusion-webui": "Incluir a flag `--api-auth` ao executar stable-diffusion-webui", "Include `--api` flag when running stable-diffusion-webui": "Incluir a flag `--api` ao executar stable-diffusion-webui", "Incomplete response": "", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive. (Default: 0.1)": "Define a rapidez com que o algoritmo responde ao feedback do texto gerado. Uma taxa de aprendizado menor resultará em ajustes mais lentos, enquanto uma taxa maior tornará o algoritmo mais responsivo. (Padrão: 0,1)", "Info": "Informação", "Input commands": "Comandos de entrada", "Install from Github URL": "Instalar da URL do Github", "Instant Auto-Send After Voice Transcription": "Envio Automático Instantâneo Após Transcrição de Voz", "Interface": "Interface", "Invalid file format.": "Formato de arquivo inválido.", "Invalid Tag": "Tag Inválida", "is typing...": "", "January": "Janeiro", "Jina API Key": "", "join our Discord for help.": "junte-se ao nosso Discord para ajudar.", "JSON": "JSON", "JSON Preview": "Pré-visualização JSON", "July": "<PERSON><PERSON>", "June": "<PERSON><PERSON>", "JWT Expiration": "Expiração do JWT", "JWT Token": "Token JWT", "Kagi Search API Key": "", "Keep Alive": "Manter Vivo", "Key": "Chave", "Keyboard shortcuts": "Atalhos de Teclado", "Knowledge": "Conhecimento", "Knowledge Access": "Acesso ao Conhecimento", "Knowledge created successfully.": "Conhecimento criado com sucesso.", "Knowledge deleted successfully.": "Conhecimento excluído com sucesso.", "Knowledge reset successfully.": "Conhecimento resetado com sucesso.", "Knowledge updated successfully": "Conhecimento atualizado com sucesso", "Label": "<PERSON><PERSON><PERSON><PERSON>", "Landing Page Mode": "Modo Landing Page", "Language": "Idioma", "Last Active": "Última Atividade", "Last Modified": "Última Modificação", "Last reply": "", "Latest users": "", "LDAP": "", "LDAP server updated": "Servidor LDAP atualizado", "Leaderboard": "Tabela de classificação", "Leave empty for unlimited": "Deixe vazio para ilimitado", "Leave empty to include all models from \"{{URL}}/api/tags\" endpoint": "Deixe vazio para incluir todos os modelos do endpoint \"{{URL}}/api/tags\"", "Leave empty to include all models from \"{{URL}}/models\" endpoint": "Deixe vazio para incluir todos os modelos do endpoint \"{{URL}}/models\"", "Leave empty to include all models or select specific models": "Deixe vazio para incluir todos os modelos ou selecione modelos especificos", "Leave empty to use the default prompt, or enter a custom prompt": "Deixe vazio para usar o prompt padr<PERSON>, ou insira um prompt personalizado", "Light": "<PERSON><PERSON><PERSON>", "Listening...": "Escutando...", "Local": "", "Local Models": "Modelos Lo<PERSON>is", "Lost": "<PERSON><PERSON><PERSON>", "LTR": "Esquerda para Direita", "Made by OpenWebUI Community": "<PERSON>ito pela Comunidade OpenWebUI", "Make sure to enclose them with": "Certifique-se de encerrá-los com", "Make sure to export a workflow.json file as API format from ComfyUI.": "Certifique-se de exportar um arquivo workflow.json como o formato API do ComfyUI.", "Manage": "Gerenciar", "Manage Arena Models": "Gerenciar Arena de Modelos", "Manage Ollama": "Gerenciar <PERSON>", "Manage Ollama API Connections": "Gerenciar Conexões Ollama API", "Manage OpenAI API Connections": "Gerenciar Conexões OpenAI API", "Manage Pipelines": "Gerenciar Pipelines", "March": "Março", "Max Tokens (num_predict)": "<PERSON><PERSON><PERSON><PERSON> (num_predict)", "Max Upload Count": "Quantidade máxima de anexos", "Max Upload Size": "Tamanho máximo do arquivo", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Máximo de 3 modelos podem ser baixados simultaneamente. Por favor, tente novamente mais tarde.", "May": "<PERSON><PERSON>", "Memories accessible by LLMs will be shown here.": "Memórias acessíveis por LLMs serão mostradas aqui.", "Memory": "Memória", "Memory added successfully": "Memória adicionada com sucesso", "Memory cleared successfully": "Memória limpa com sucesso", "Memory deleted successfully": "Memória excluída com sucesso", "Memory updated successfully": "Memória atualizada com sucesso", "Merge Responses": "Mesclar respostas", "Message rating should be enabled to use this feature": "Mensagem de avaliação deve estar habilitada para usar esta função", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "Mensagens enviadas após criar seu link não serão compartilhadas. Usuários com o URL poderão visualizar o chat compartilhado.", "Min P": "", "Minimum Score": "Pontuação Mínima", "Mirostat": "Mirostat", "Mirostat Eta": "Mirostat Eta", "Mirostat Tau": "Mirostat Tau", "MMMM DD, YYYY": "MMMM DD, YYYY", "MMMM DD, YYYY HH:mm": "MMMM DD, YYYY HH:mm", "MMMM DD, YYYY hh:mm:ss A": "MMMM DD, YYYY hh:mm:ss A", "Model": "<PERSON><PERSON>", "Model '{{modelName}}' has been successfully downloaded.": "Modelo '{{modelName}}' foi baixado com sucesso.", "Model '{{modelTag}}' is already in queue for downloading.": "Modelo '{{modelTag}}' já está na fila para download.", "Model {{modelId}} not found": "Modelo {{modelId}} não encontrado", "Model {{modelName}} is not vision capable": "Modelo {{modelName}} não é capaz de visão", "Model {{name}} is now {{status}}": "Modelo {{name}} est<PERSON> agora {{status}}", "Model accepts image inputs": "Modelo aceita entradas de imagens", "Model created successfully!": "Modelo criado com sucesso!", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Caminho do sistema de arquivos do modelo detectado. Nome curto do modelo é necessário para atualização, não é possível continuar.", "Model Filtering": "Filtrando modelo", "Model ID": "ID do Modelo", "Model IDs": "IDs do modelo", "Model Name": "Nome do Modelo", "Model not selected": "Modelo não selecionado", "Model Params": "Parâmetros do Modelo", "Model Permissions": "Permissões do Modelo", "Model updated successfully": "Modelo atualizado com sucesso", "Modelfile Content": "Conteúdo do Arquivo do Modelo", "Models": "Modelos", "Models Access": "Acesso aos Modelos", "Models configuration saved successfully": "", "Mojeek Search API Key": "Chave de API Mojeel Search", "more": "mais", "More": "<PERSON><PERSON>", "Name": "Nome", "Name your knowledge base": "Nome da sua base de conhecimento", "New Chat": "Novo Chat", "New folder": "", "New Password": "Nova Senha", "new-channel": "", "No content found": "Nenhum conteúdo encontrado", "No content to speak": "Sem conteúdo para falar", "No distance available": "Sem distância disponível", "No feedbacks found": "Comentários não encontrados", "No file selected": "Nenhum arquivo selecionado", "No files found.": "Nenhum arquivo encontrado.", "No groups with access, add a group to grant access": "Nenhum grupo com acesso, adicione um grupo para dar acesso", "No HTML, CSS, or JavaScript content found.": "Nenhum conteúdo HTML, CSS ou JavaScript encontrado.", "No knowledge found": "Nenhum conhecimento encontrado", "No model IDs": "Nenhum ID de modelo", "No models found": "Nenhum modelo encontrado", "No models selected": "", "No results found": "Nenhum resultado encontrado", "No search query generated": "Nenhuma consulta de pesquisa gerada", "No source available": "Nenhuma fonte disponível", "No users were found.": "Nenhum usuário foi encontrado.", "No valves to update": "Nenhuma válvula para atualizar", "None": "<PERSON><PERSON><PERSON>", "Not accurate": "", "Not relevant": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Nota: Se você definir uma pontuação mínima, a pesquisa retornará apenas documentos com pontuação igual ou superior à pontuação mínima.", "Notes": "Notas", "Notification Sound": "", "Notification Webhook": "", "Notifications": "Notificações", "November": "Novembro", "num_gpu (Ollama)": "Número de GPUs (Ollama)", "num_thread (Ollama)": "<PERSON><PERSON><PERSON><PERSON>ead<PERSON> (Ollama)", "OAuth ID": "OAuth ID", "October": "Out<PERSON>ro", "Off": "Des<PERSON><PERSON>", "Okay, Let's Go!": "Ok, Vamos Lá!", "OLED Dark": "OLED Escuro", "Ollama": "Ollama", "Ollama API": "API Ollama", "Ollama API disabled": "API Ollama desativada", "Ollama API settings updated": "Configurações da API Ollama atualizadas", "Ollama Version": "<PERSON><PERSON><PERSON>", "On": "Ligado", "Only alphanumeric characters and hyphens are allowed": "Somente caracteres alfanuméricos e hífens são permitidos", "Only alphanumeric characters and hyphens are allowed in the command string.": "Apenas caracteres alfanuméricos e hífens são permitidos na string de comando.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "Somente coleções podem ser editadas. Crie uma nova base de conhecimento para editar/adicionar documentos.", "Only select users and groups with permission can access": "Somente usuários e grupos selecionados com permissão podem acessar.", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Ops! Parece que a URL é inválida. Por favor, verifique novamente e tente de novo.", "Oops! There are files still uploading. Please wait for the upload to complete.": "Ops! Existem arquivos a serem carregados. Por favor, aguarde que o carregamento tenha concluído.", "Oops! There was an error in the previous response.": "Ops! Houve um erro na resposta anterior.", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Ops! Você está usando um método não suportado (somente frontend). Por favor, sirva a WebUI a partir do backend.", "Open in full screen": "A<PERSON>r em tela cheia", "Open new chat": "Abrir novo chat", "Open WebUI uses faster-whisper internally.": "Open WebUI usa faster-whisper internamente.", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "A Open WebUI usa os embeddings de voz do SpeechT5 e do CMU Arctic.", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "A versão do Open WebUI (v{{OPEN_WEBUI_VERSION}}) é inferior à versão necessária (v{{REQUIRED_VERSION}})", "OpenAI": "OpenAI", "OpenAI API": "API OpenAI", "OpenAI API Config": "Configuração da API OpenAI", "OpenAI API Key is required.": "Chave API OpenAI é necessária.", "OpenAI API settings updated": "Configurações OpenAI atualizadas", "OpenAI URL/Key required.": "URL/Chave OpenAI necessária.", "or": "ou", "Organize your users": "Organizar seus usuários", "OUTPUT": "SAÍDA", "Output format": "Formato de saída", "Overview": "Visão Geral", "page": "p<PERSON><PERSON><PERSON>", "Password": "<PERSON><PERSON>", "Paste Large Text as File": "Cole Textos Longos como Arquivo", "PDF document (.pdf)": "Documento PDF (.pdf)", "PDF Extract Images (OCR)": "Extrair Imagens do PDF (OCR)", "pending": "pendente", "Permission denied when accessing media devices": "Permissão negada ao acessar dispositivos de mídia", "Permission denied when accessing microphone": "Permissão negada ao acessar o microfone", "Permission denied when accessing microphone: {{error}}": "Permissão negada ao acessar o microfone: {{error}}", "Permissions": "Permissões", "Personalization": "Personalização", "Pin": "Fixar", "Pinned": "Fixado", "Pioneer insights": "Insights pioneiros", "Pipeline deleted successfully": "Pipeline excluído com sucesso", "Pipeline downloaded successfully": "Pipeline baixado com sucesso", "Pipelines": "Pipelines", "Pipelines Not Detected": "Pipelines Não Detectados", "Pipelines Valves": "Válvulas de Pipelines", "Plain text (.txt)": "Texto simples (.txt)", "Playground": "Playground", "Please carefully review the following warnings:": "Por favor, revise cuidadosamente os seguintes avisos:", "Please enter a prompt": "Por favor, digite um prompt", "Please fill in all fields.": "Por favor, preencha todos os campos.", "Please select a model first.": "", "Port": "Porta", "Prefix ID": "Prefixo ID", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "O ID de prefixo é utilizado para evitar conflitos com outras conexões, adicionando um prefixo aos IDs dos modelos - deixe em branco para desativar.", "Previous 30 days": "Últimos 30 dias", "Previous 7 days": "Últimos 7 dias", "Profile Image": "<PERSON><PERSON>", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Prompt (por exemplo, Diga-me um fato divertido sobre o Império Romano)", "Prompt Content": "Conteúdo do Prompt", "Prompt created successfully": "Prompt criado com sucesso", "Prompt suggestions": "Sugestões de Prompt", "Prompt updated successfully": "Prompt atualizado com sucesso", "Prompts": "Prompts", "Prompts Access": "Acessar prompts", "Provide any specific details": "", "Proxy URL": "", "Pull \"{{searchValue}}\" from Ollama.com": "Obter \"{{searchValue}}\" de Ollama.com", "Pull a model from Ollama.com": "Obter um modelo de Ollama.com", "Query Generation Prompt": "Prompt de Geração de Consulta", "Query Params": "Parâmetros de Consulta", "RAG Template": "Modelo RAG", "Rating": "Avaliação", "Re-rank models by topic similarity": "Reclassificação de modelos por similaridade de tópico", "Read Aloud": "Ler <PERSON> Voz Alta", "Record voice": "<PERSON><PERSON><PERSON> voz", "Redirecting you to OpenWebUI Community": "Redirecionando você para a Comunidade OpenWebUI", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative. (Default: 40)": "Reduz a probabilidade de gerar absurdos. Um valor mais alto (por exemplo, 100) dará respostas mais diversas, enquanto um valor mais baixo (por exemplo, 10) será mais conservador. (Padr<PERSON>: 40)", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "Refira-se como \"Usuário\" (por exemplo, \"Usuário está aprendendo espanhol\")", "References from": "Referências de", "Refresh Token Expiration": "", "Regenerate": "Gerar novamente", "Release Notes": "Notas de Lançamento", "Relevance": "Relevância", "Remove": "Remover", "Remove Model": "Remover Modelo", "Rename": "Renomear", "Reorder Models": "", "Repeat Last N": "Repetir Último <PERSON>", "Reply in Thread": "", "Request Mode": "Modo de Solicitação", "Reranking Model": "Modelo de Reclassificação", "Reranking model disabled": "Modelo de Reclassificação desativado", "Reranking model set to \"{{reranking_model}}\"": "Modelo de Reclassificação definido como \"{{reranking_model}}\"", "Reset": "Redefinir", "Reset All Models": "", "Reset Upload Directory": "Redefinir Diretório de Upload", "Reset Vector Storage/Knowledge": "Redefinir Armazenamento de Vetores/Conhecimento", "Reset view": "", "Response generation stopped": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "Notificações de resposta não podem ser ativadas pois as permissões do site foram negadas. Por favor, visite as configurações do seu navegador para conceder o acesso necessário.", "Response splitting": "Divisão da Resposta", "Result": "<PERSON><PERSON><PERSON><PERSON>", "Retrieval Query Generation": "", "Rich Text Input for Chat": "Entrada de rich text para bate-papo", "RK": "", "Role": "Função", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "Direita para Esquerda", "Run": "Executar", "Running": "Executando", "Save": "<PERSON><PERSON>", "Save & Create": "<PERSON>var e Criar", "Save & Update": "Salvar e Atualizar", "Save As Copy": "Salvar Como Cópia", "Save Tag": "<PERSON><PERSON>", "Saved": "Armazenado", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Salvar registros de chat diretamente no armazenamento do seu navegador não é mais suportado. Por favor, reserve um momento para baixar e excluir seus registros de chat clicando no botão abaixo. Não se preocupe, você pode facilmente reimportar seus registros de chat para o backend através de", "Scroll to bottom when switching between branches": "Rolar para baixo quando se troca entre modelos", "Search": "<PERSON><PERSON><PERSON><PERSON>", "Search a model": "Pesquisar um modelo", "Search Base": "Pesquisar base", "Search Chats": "<PERSON><PERSON><PERSON><PERSON>", "Search Collection": "Pesquisar <PERSON>", "Search Filters": "<PERSON><PERSON><PERSON><PERSON>", "search for tags": "Pesquisar por tags", "Search Functions": "Pesquisar <PERSON>", "Search Knowledge": "Pesquisar <PERSON>", "Search Models": "<PERSON><PERSON><PERSON><PERSON>", "Search options": "Opções de pesquisa", "Search Prompts": "Prompts de Pesquisa", "Search Result Count": "Contagem de Resultados da Pesquisa", "Search the web": "Pesquisar web", "Search Tools": "<PERSON><PERSON><PERSON><PERSON>", "Search users": "", "SearchApi API Key": "Chave API SearchApi", "SearchApi Engine": "Motor SearchApi", "Searched {{count}} sites": "", "Searching \"{{searchQuery}}\"": "<PERSON><PERSON><PERSON><PERSON><PERSON> \"{{searchQuery}}\"", "Searching Knowledge for \"{{searchQuery}}\"": "Buscando conhecimento para \"{{searchQuery}}\"", "Searxng Query URL": "URL da Consulta Searxng", "See readme.md for instructions": "<PERSON><PERSON>a readme.md para instruções", "See what's new": "Veja o que há de novo", "Seed": "Seed", "Select a base model": "Selecione um modelo base", "Select a engine": "Selecione um motor", "Select a function": "Selecione uma função", "Select a group": "Selecionar grupo", "Select a model": "Selecione um modelo", "Select a pipeline": "Selecione um pipeline", "Select a pipeline url": "Selecione uma URL de pipeline", "Select a tool": "Selecione uma ferramenta", "Select Engine": "Selecionar Motor", "Select Knowledge": "Selecionar Conhecimento", "Select model": "Selecionar modelo", "Select only one model to call": "Selecione apenas um modelo para chamar", "Selected model(s) do not support image inputs": "Modelo(s) selecionado(s) não suportam entradas de imagem", "Semantic distance to query": "Distância semântica para consulta", "Send": "Enviar", "Send a message": "", "Send a Message": "Enviar uma Mensagem", "Send message": "Enviar mensagem", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "Envia `stream_options: { include_usage: true }` na solicitação. Provedores compatíveis retornarão informações sobre o uso de tokens na resposta quando configurado.", "September": "Setembro", "Serper API Key": "<PERSON><PERSON> da <PERSON>", "Serply API Key": "<PERSON><PERSON> da <PERSON>", "Serpstack API Key": "<PERSON><PERSON> da <PERSON>", "Server connection verified": "Conexão com o servidor verificada", "Set as default": "Definir como padrão", "Set CFG Scale": "Definir escala CFG", "Set Default Model": "Definir Modelo <PERSON>", "Set embedding model": "Definir modelo de embedding", "Set embedding model (e.g. {{model}})": "Definir modelo de embedding (por exemplo, {{model}})", "Set Image Size": "<PERSON><PERSON><PERSON>", "Set reranking model (e.g. {{model}})": "Definir modelo de reclassificação (por exemplo, {{model}})", "Set Sampler": "<PERSON><PERSON><PERSON>", "Set Scheduler": "Definir Agendador", "Set Steps": "Definir Etapas", "Set Task Model": "Definir Modelo de Tarefa", "Set the number of GPU devices used for computation. This option controls how many GPU devices (if available) are used to process incoming requests. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "Defina o número de dispositivos GPU usados para computação. Esta opção controla quantos dispositivos GPU (se disponíveis) são usados para processar as solicitações recebidas. Aumentar esse valor pode melhorar significativamente o desempenho para modelos otimizados para aceleração de GPU, mas também pode consumir mais energia e recursos da GPU.", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "Defina o número de threads de trabalho usadas para computação. Esta opção controla quantos threads são usados para processar as solicitações recebidas de forma simultânea. Aumentar esse valor pode melhorar o desempenho em cargas de trabalho de alta concorrência, mas também pode consumir mais recursos da CPU.", "Set Voice": "<PERSON><PERSON><PERSON>", "Set whisper model": "Definir modelo Whisper", "Sets how far back for the model to look back to prevent repetition. (Default: 64, 0 = disabled, -1 = num_ctx)": "Define a distância de retrocesso que o modelo deve olhar para evitar repetições. (Padrão: 64, 0 = desativado, -1 = num_ctx)", "Sets how strongly to penalize repetitions. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. (Default: 1.1)": "Define com que intensidade as repetições serão penalizadas. Um valor mais alto (por exemplo, 1,5) penalizará as repetições mais fortemente, enquanto um valor mais baixo (por exemplo, 0,9) será mais permissivo. (Padrão: 1,1)", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt. (Default: random)": "Define a semente do número aleatório a ser usada para a geração. Definir isso como um número específico fará com que o modelo gere o mesmo texto para o mesmo prompt. (Padrão: aleatório)", "Sets the size of the context window used to generate the next token. (Default: 2048)": "Define o tamanho da janela de contexto usada para gerar o próximo token. (Padrão: 2048)", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "Define as sequências de parada a serem usadas. <PERSON>uando esse padrão for encontrado, o modelo de linguagem (LLM) parará de gerar texto e retornará. Vários padrões de parada podem ser definidos especificando parâmetros de parada separados em um arquivo de modelo.", "Settings": "Configurações", "Settings saved successfully!": "Configurações salvas com sucesso!", "Share": "Compartilhar", "Share Chat": "Comp<PERSON><PERSON><PERSON>", "Share to OpenWebUI Community": "Compartilhar com a Comunidade OpenWebUI", "Show": "Mostrar", "Show \"What's New\" modal on login": "Mostrar \"O que há de Novo\" no login", "Show Admin Details in Account Pending Overlay": "Mostrar Detalhes do Administrador na Sobreposição de Conta Pendentes", "Show shortcuts": "<PERSON><PERSON> atalhos", "Show your support!": "Mostre seu apoio!", "Sign in": "Entrar", "Sign in to {{WEBUI_NAME}}": "Faça login em {{WEBUI_NAME}}", "Sign in to {{WEBUI_NAME}} with LDAP": "Faça login em {{WEBUI_NAME}} com LDAP", "Sign Out": "<PERSON><PERSON>", "Sign up": "Inscrever-se", "Sign up to {{WEBUI_NAME}}": "Inscreva-se em {{WEBUI_NAME}}", "Signing in to {{WEBUI_NAME}}": "Fazendo login em {{WEBUI_NAME}}", "sk-1234": "", "Source": "Fonte", "Speech Playback Speed": "Velocidade de reprodução de fala", "Speech recognition error: {{error}}": "Erro de reconhecimento de fala: {{error}}", "Speech-to-Text Engine": "Motor de Transcrição de Fala", "Stop": "<PERSON><PERSON>", "Stop Sequence": "Sequência de Parada", "Stream Chat Response": "Stream Resposta do Chat", "STT Model": "Modelo STT", "STT Settings": "Configurações STT", "Success": "Sucesso", "Successfully updated.": "Atualizado com sucesso.", "Suggested prompts to get you started": "", "Support": "Suporte", "Support this plugin:": "Apoie este plugin:", "Sync directory": "", "System": "Sistema", "System Instructions": "Instruções do sistema", "System Prompt": "Prompt do Sistema", "Tags Generation": "", "Tags Generation Prompt": "Prompt para geração de Tags", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting. (default: 1)": "A amostragem *tail free* é usada para reduzir o impacto de tokens menos prováveis na saída. Um valor mais alto (por exemplo, 2,0) reduzirá mais o impacto, enquanto um valor de 1,0 desativa essa configuração. (Padrão: 1)", "Tap to interrupt": "Toque para interromper", "Tavily API Key": "<PERSON>ve da <PERSON>", "Temperature": "Temperatura", "Template": "Template", "Temporary Chat": "Chat tempor<PERSON><PERSON>", "Text Splitter": "Divisor de Texto", "Text-to-Speech Engine": "Motor de Texto para Fala", "Tfs Z": "Tfs Z", "Thanks for your feedback!": "<PERSON><PERSON><PERSON> pelo seu comentário!", "The Application Account DN you bind with for search": "O DN (Distinguished Name) da Conta de Aplicação com a qual você se conecta para pesquisa.", "The base to search for users": "Base para pesquisar usuários.", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.  (Default: 512)": "O tamanho do lote (batch size) determina quantas solicitações de texto são processadas juntas de uma vez. Um tamanho de lote maior pode aumentar o desempenho e a velocidade do modelo, mas também requer mais memória. (Padrão: 512)", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "Os desenvolvedores por trás deste plugin são voluntários apaixonados da comunidade. Se você achar este plugin útil, considere contribuir para o seu desenvolvimento.", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "A evolução do ranking de avaliação é baseada no sistema Elo e será atualizada em tempo real.", "The LDAP attribute that maps to the username that users use to sign in.": "O atributo LDAP que mapeia para o nome de usuário que os usuários usam para fazer login.", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "O ranking atual está em beta, e podemos ajustar as contas de avaliação como refinamos o algoritmo.", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "Máximo tamanho de arquivo em MB. Se o tamanho do arquivo exceder este limite, o arquivo não será enviado.", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "O número máximo de arquivos que podem ser utilizados a cada vez em chat. Se o número de arquivos exceder este limite, os arquivos não serão enviados.", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "A pontuação deve ser um valor entre 0.0 (0%) e 1.0 (100%).", "The temperature of the model. Increasing the temperature will make the model answer more creatively. (Default: 0.8)": "Temperatura do modelo. Aumentar a temperatura fará com que o modelo responda de forma mais criativa. (Padrão: 0,8)", "Theme": "<PERSON><PERSON>", "Thinking...": "Pensando...", "This action cannot be undone. Do you wish to continue?": "Esta ação não pode ser desfeita. Você deseja continuar?", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "<PERSON><PERSON> garante que suas conversas valiosas sejam salvas com segurança no banco de dados do backend. Obrigado!", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "Esta é uma funcionalidade experimental, pode não funcionar como esperado e está sujeita a alterações a qualquer momento.", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics. (Default: 24)": "Esta opção controla quantos tokens são preservados ao atualizar o contexto. <PERSON><PERSON> exemplo, se definido como 2, os últimos 2 tokens do contexto da conversa serão mantidos. Preservar o contexto pode ajudar a manter a continuidade da conversa, mas pode reduzir a capacidade de responder a novos tópicos. (Padrão: 24)", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.  (Default: 128)": "Esta opção define o número máximo de tokens que o modelo pode gerar em sua resposta. Aumentar esse limite permite que o modelo forneça respostas mais longas, mas também pode aumentar a probabilidade de gerar conteúdo irrelevante ou não útil. (Padrão: 128)", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "Essa opção deletará todos os arquivos existentes na coleção e todos eles serão substituídos.", "This response was generated by \"{{model}}\"": "Esta resposta foi gerada por \"{{model}}\"", "This will delete": "Is<PERSON> vai excluir", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "Esta ação excluirá <strong>{{NAME}}</strong> e <strong>todos seus conteúdos</strong>.", "This will delete all models including custom models": "Isto vai excluir todos os modelos, incluindo personalizados", "This will delete all models including custom models and cannot be undone.": "Isto vai excluir todos os modelos, incluindo personalizados e não pode ser desfeito.", "This will reset the knowledge base and sync all files. Do you wish to continue?": "Esta ação resetará a base de conhecimento e sincronizará todos os arquivos. Deseja continuar?", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "URL do servidor Tika necessária.", "Tiktoken": "", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Dica: Atualize vários slots de variáveis consecutivamente pressionando a tecla Tab na entrada de chat após cada substituição.", "Title": "<PERSON><PERSON><PERSON><PERSON>", "Title (e.g. Tell me a fun fact)": "T<PERSON><PERSON>lo (por exemplo, Conte-me um fato divertido)", "Title Auto-Generation": "Geração Automática de Título", "Title cannot be an empty string.": "O Título não pode ser uma string vazia.", "Title Generation Prompt": "Prompt de Geração de Título", "TLS": "", "To access the available model names for downloading,": "Para acessar os nomes de modelos disponíveis para download,", "To access the GGUF models available for downloading,": "Para acessar os modelos GGUF disponíveis para download,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "Para acessar a WebUI, entre em contato com o administrador. Os administradores podem gerenciar os status dos usuários no Painel de Administração.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "Para anexar a base de conhecimento aqui, adicione-os ao espaço de trabalho \"Conhecimento\" primeiro.", "To learn more about available endpoints, visit our documentation.": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "Para proteger sua privacidade, apenas classificações, IDs de modelo, tags e metadados são compartilhados a partir de seus comentários – seus registros de bate-papo permanecem privados e não são incluídos.", "To select actions here, add them to the \"Functions\" workspace first.": "Para selecionar ações aqui, adicione-os ao espaço de trabalho \"Ações\" primeiro.", "To select filters here, add them to the \"Functions\" workspace first.": "Para selecionar filtros aqui, adicione-os ao espaço de trabalho \"Funções\" primeiro.", "To select toolkits here, add them to the \"Tools\" workspace first.": "Para selecionar kits de ferramentas aqui, adicione-os ao espaço de trabalho \"Ferramentas\" primeiro.", "Toast notifications for new updates": "Notificações de alerta para novas atualizações", "Today": "Hoje", "Toggle settings": "Alternar configurações", "Toggle sidebar": "Alternar barra lateral", "Token": "", "Tokens To Keep On Context Refresh (num_keep)": "Tokens a Manter na Atualização do Contexto (num_keep)", "Tool created successfully": "Ferramenta criada com sucesso", "Tool deleted successfully": "Ferramenta excluída com sucesso", "Tool Description": "Descrição da ferramenta", "Tool ID": "ID da ferramenta", "Tool imported successfully": "Ferramenta importada com sucesso", "Tool Name": "Nome da ferramenta", "Tool updated successfully": "Ferramenta atualizada com sucesso", "Tools": "Ferramentas", "Tools Access": "<PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>", "Tools are a function calling system with arbitrary code execution": "Ferramentas são um sistema de chamada de funções com execução de código arbitrário", "Tools have a function calling system that allows arbitrary code execution": "Ferramentas possuem um sistema de chamada de funções que permite a execução de código arbitrário", "Tools have a function calling system that allows arbitrary code execution.": "Ferramentas possuem um sistema de chamada de funções que permite a execução de código arbitrário.", "Top K": "Top K", "Top P": "Top P", "Transformers": "", "Trouble accessing Ollama?": "Problemas para acessar o Ollama?", "TTS Model": "Modelo TTS", "TTS Settings": "Configurações TTS", "TTS Voice": "Voz TTS", "Type": "Tipo", "Type Hugging Face Resolve (Download) URL": "Digite o URL de download do Hugging Face", "Uh-oh! There was an issue with the response.": "", "UI": "Interface", "Unarchive All": "<PERSON>ar<PERSON><PERSON> tudo", "Unarchive All Archived Chats": "Desarquivar Todos os Chats Arquivados", "Unarchive Chat": "<PERSON><PERSON><PERSON><PERSON>", "Unlock mysteries": "<PERSON><PERSON><PERSON>", "Unpin": "Desfixar", "Unravel secrets": "<PERSON><PERSON><PERSON>", "Untagged": "Sem tag", "Update": "<PERSON><PERSON><PERSON><PERSON>", "Update and Copy Link": "Atualizar e Copiar Link", "Update for the latest features and improvements.": "Atualizar para as novas funcionalidades e melhorias.", "Update password": "<PERSON><PERSON><PERSON><PERSON>", "Updated": "Atualizado", "Updated at": "Atualizado em", "Updated At": "Atualizado Em", "Upload": "Fazer upload", "Upload a GGUF model": "Fazer upload de um modelo GGUF", "Upload directory": "<PERSON><PERSON><PERSON>", "Upload files": "<PERSON><PERSON><PERSON>", "Upload Files": "Fazer upload de Arquivos", "Upload Pipeline": "Fazer upload de Pipeline", "Upload Progress": "Progresso do Upload", "URL": "", "URL Mode": "Modo URL", "Use '#' in the prompt input to load and include your knowledge.": "Usar '#' no prompt para carregar e incluir seus conhecimentos.", "Use Gravatar": "<PERSON><PERSON>", "Use groups to group your users and assign permissions.": "Use grupos para agrupar seus usuários e atribuir permissões.", "Use Initials": "Usar Iniciais", "use_mlock (Ollama)": "use_mlock (Ollama)", "use_mmap (Ollama)": "use_mmap (Ollama)", "user": "<PERSON><PERSON><PERSON><PERSON>", "User": "<PERSON><PERSON><PERSON><PERSON>", "User location successfully retrieved.": "Localização do usuário recuperada com sucesso.", "Username": "Nome do Usuário", "Users": "Usuários", "Using the default arena model with all models. Click the plus button to add custom models.": "Usando a arena de modelos padrão para todos os modelos. Clique no botão mais para adicionar modelos personalizados.", "Utilize": "<PERSON><PERSON><PERSON><PERSON>", "Valid time units:": "Unidades de tempo válidas:", "Valves": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Valves updated": "Válvulas atualizadas", "Valves updated successfully": "Válvulas atualizadas com sucesso", "variable": "<PERSON><PERSON><PERSON><PERSON>", "variable to have them replaced with clipboard content.": "variável para ser substituída pelo conteúdo da área de transferência.", "Version": "Vers<PERSON>", "Version {{selectedVersion}} of {{totalVersions}}": "Versão {{selectedVersion}} de {{totalVersions}}", "Very bad": "", "View Replies": "", "Visibility": "Visibilidade", "Voice": "Voz", "Voice Input": "Entrada de voz", "Warning": "Aviso", "Warning:": "Aviso:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "Aviso: Habilitar isso permitirá que os usuários façam upload de código arbitrário no servidor.", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Aviso: Se você atualizar ou alterar seu modelo de incorporação, será necessário reimportar todos os documentos.", "Web": "Web", "Web API": "API Web", "Web Loader Settings": "Configurações do Carregador Web", "Web Search": "Pesquisa na Web", "Web Search Engine": "Mecanismo de Busca na Web", "Web Search Query Generation": "", "Webhook URL": "URL do Webhook", "WebUI Settings": "Configurações da WebUI", "WebUI URL": "", "WebUI will make requests to \"{{url}}/api/chat\"": "A WebUI fará requisições para \"{{url}}/api/chat\".", "WebUI will make requests to \"{{url}}/chat/completions\"": "A WebUI fará requisições para \"{{url}}/chat/completions\".", "What are you trying to achieve?": "O que está tentando alcançar?", "What are you working on?": "No que está trabalhando?", "What didn't you like about this response?": "", "What’s New in": "O que há de novo em", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "<PERSON>uando habilitado, o modelo responderá a cada mensagem de chat em tempo real, gerando uma resposta assim que o usuário enviar uma mensagem. Este modo é útil para aplicativos de chat ao vivo, mas pode impactar o desempenho em hardware mais lento.", "wherever you are": "onde quer que você esteja.", "Whisper (Local)": "<PERSON><PERSON><PERSON> (Local)", "Widescreen Mode": "Modo Tela Cheia", "Won": "Ganhou", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text. (Default: 0.9)": "Funciona em conjunto com o top-k. Um valor mais alto (por exemplo, 0,95) levar<PERSON> a um texto mais diversificado, enquanto um valor mais baixo (por exemplo, 0,5) gerará um texto mais focado e conservador. (Padrão: 0,9)", "Workspace": "Espaço de Trabalho", "Workspace Permissions": "Permissões do espaço de trabalho", "Write a prompt suggestion (e.g. Who are you?)": "Escreva uma sugestão de prompt (por exemplo, Quem é você?)", "Write a summary in 50 words that summarizes [topic or keyword].": "Escreva um resumo em 50 palavras que resuma [tópico ou palavra-chave].", "Write something...": "Escreva algo...", "Write your model template content here": "Escreva o conteúdo do template do modelo aqui.", "Yesterday": "Ontem", "You": "Você", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "Você só pode conversar com no máximo {{maxCount}} arquivo(s) de cada vez.", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "Você pode personalizar suas interações com LLMs adicionando memórias através do botão 'Gerenciar' abaixo, tornando-as mais <PERSON> e adaptadas a você.", "You cannot upload an empty file.": "Você não pode carregar um arquivo vazio.", "You have no archived conversations.": "Você não tem conversas arquivadas.", "You have shared this chat": "Você compartilhou este chat", "You're a helpful assistant.": "Você é um assistente útil.", "Your account status is currently pending activation.": "O status da sua conta está atualmente aguardando ativação.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "Toda a sua contribuição irá diretamente para o desenvolvedor do plugin; o Open WebUI não retém nenhuma porcentagem. No entanto, a plataforma de financiamento escolhida pode ter suas próprias taxas.", "Youtube": "Youtube", "Youtube Loader Settings": "Configurações do Carregador Youtube"}