{"-1 for no limit, or a positive integer for a specific limit": "-1 表示無限制，或正整數表示特定限制", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s'、'm'、'h'、'd'、'w' 或 '-1' 表示無到期時間。", "(e.g. `sh webui.sh --api --api-auth username_password`)": "（例如 `sh webui.sh --api --api-auth username_password`）", "(e.g. `sh webui.sh --api`)": "（例如 `sh webui.sh --api`）", "(latest)": "（最新版）", "{{ models }}": "{{ models }}", "{{COUNT}} Replies": "", "{{user}}'s Chats": "{{user}} 的對話", "{{webUIName}} Backend Required": "需要 {{webUIName}} 後端", "*Prompt node ID(s) are required for image generation": "* 圖片生成需要提示詞節點 ID", "A new version (v{{LATEST_VERSION}}) is now available.": "新版本 (v{{LATEST_VERSION}}) 現已釋出。", "A task model is used when performing tasks such as generating titles for chats and web search queries": "執行產生對話標題和網頁搜尋查詢等任務時會使用任務模型", "a user": "一位使用者", "About": "關於", "Access": "存取", "Access Control": "存取控制", "Accessible to all users": "所有使用者可存取", "Account": "帳號", "Account Activation Pending": "帳號待啟用", "Actions": "動作", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "在對話輸入框中輸入 \"/{{COMMAND}}\" 來啟用此命令。", "Active Users": "活躍使用者", "Add": "新增", "Add a model ID": "新增模型 ID", "Add a short description about what this model does": "新增這個模型的簡短描述", "Add a tag": "新增標籤", "Add Arena Model": "新增競技模型", "Add Connection": "新增連線", "Add Content": "新增內容", "Add content here": "在此新增內容", "Add custom prompt": "新增自訂提示詞", "Add Files": "新增檔案", "Add Group": "新增群組", "Add Memory": "新增記憶", "Add Model": "新增模型", "Add Reaction": "", "Add Tag": "新增標籤", "Add Tags": "新增標籤", "Add text content": "新增文字內容", "Add User": "新增使用者", "Add User Group": "新增使用者群組", "Adjusting these settings will apply changes universally to all users.": "調整這些設定將會影響所有使用者。", "admin": "管理員", "Admin": "管理員", "Admin Panel": "管理員控制台", "Admin Settings": "管理員設定", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "管理員可以隨時使用所有工具；使用者則需在工作區中為每個模型分配工具。", "Advanced Parameters": "進階參數", "Advanced Params": "進階參數", "All Documents": "所有文件", "All models deleted successfully": "成功刪除所有模型", "Allow Chat Delete": "允許刪除對話", "Allow Chat Deletion": "允許刪除對話紀錄", "Allow Chat Edit": "允許編輯對話", "Allow File Upload": "允許上傳檔案", "Allow non-local voices": "允許非本機語音", "Allow Temporary Chat": "允許暫時對話", "Allow User Location": "允許使用者位置", "Allow Voice Interruption in Call": "允許在通話中打斷語音", "Allowed Endpoints": "允許的端點", "Already have an account?": "已經有帳號了嗎？", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out. (Default: 0.0)": "作為 top_p 的替代方案，旨在確保質量和多樣性的平衡。相對於最可能的 token 機率而言，參數 p 代表一個 token 被考慮在内的最低機率。例如，當 p=0.05 且最可能的 token 機率為 0.9 時，數值低於 0.045 的對數機率會被過濾掉。（預設值：0.0）", "an assistant": "一位助手", "and": "和", "and {{COUNT}} more": "和另外 {{COUNT}} 個", "and create a new shared link.": "並建立新的共用連結。", "API Base URL": "API 基礎 URL", "API Key": "API 金鑰", "API Key created.": "API 金鑰已建立。", "API Key Endpoint Restrictions": "API 金鑰端點限制", "API keys": "API 金鑰", "Application DN": "應用程式 DN", "Application DN Password": "應用程式 DN 密碼", "applies to all users with the \"user\" role": "適用於所有具有「使用者」角色的使用者", "April": "4 月", "Archive": "封存", "Archive All Chats": "封存所有對話紀錄", "Archived Chats": "封存的對話紀錄", "archived-chat-export": "archived-chat-export", "Are you sure you want to delete this channel?": "您確定要刪除此頻道嗎？", "Are you sure you want to delete this message?": "您確定要刪除此訊息嗎？", "Are you sure you want to unarchive all archived chats?": "您確定要解除封存所有封存的對話記錄嗎？", "Are you sure?": "您確定嗎？", "Arena Models": "競技模型", "Artifacts": "成品", "Ask a question": "提出問題", "Assistant": "助手", "Attach file": "附加檔案", "Attribute for Username": "使用者名稱屬性", "Audio": "音訊", "August": "8 月", "Authenticate": "驗證", "Auto-Copy Response to Clipboard": "自動將回應複製到剪貼簿", "Auto-playback response": "自動播放回應", "Autocomplete Generation": "自動完成生成", "Autocomplete Generation Input Max Length": "自動完成產生輸入最大長度", "Automatic1111": "Automatic1111", "AUTOMATIC1111 Api Auth String": "AUTOMATIC1111 API 驗證字串", "AUTOMATIC1111 Base URL": "AUTOMATIC1111 基礎 URL", "AUTOMATIC1111 Base URL is required.": "需要 AUTOMATIC1111 基礎 URL。", "Available list": "可用清單", "available!": "可用！", "Azure AI Speech": "Azure AI 語音", "Azure Region": "Azure 區域", "Back": "返回", "Bad": "", "Bad Response": "錯誤回應", "Banners": "橫幅", "Base Model (From)": "基礎模型（來自）", "Batch Size (num_batch)": "批次大小（num_batch）", "before": "之前", "Beta": "測試", "BETA": "", "Bing Search V7 Endpoint": "Bing 搜尋 V7 端點", "Bing Search V7 Subscription Key": "Bing 搜尋 V7 訂閱金鑰", "Brave Search API Key": "Brave 搜尋 API 金鑰", "By {{name}}": "由 {{name}} 製作", "Bypass SSL verification for Websites": "略過網站的 SSL 驗證", "Call": "通話", "Call feature is not supported when using Web STT engine": "使用網頁語音辨識 (Web STT) 引擎時不支援通話功能", "Camera": "相機", "Cancel": "取消", "Capabilities": "功能", "Capture": "擷取", "Certificate Path": "憑證路徑", "Change Password": "修改密碼", "Channel Name": "頻道名稱", "Channels": "頻道", "Character": "角色", "Character limit for autocomplete generation input": "自動完成產生輸入的字元限制", "Chart new frontiers": "探索新領域", "Chat": "對話", "Chat Background Image": "對話背景圖片", "Chat Bubble UI": "對話氣泡介面", "Chat Controls": "對話控制項", "Chat direction": "對話方向", "Chat Overview": "對話概覽", "Chat Permissions": "對話權限", "Chat Tags Auto-Generation": "對話標籤自動生成", "Chats": "對話", "Check Again": "再次檢查", "Check for updates": "檢查更新", "Checking for updates...": "正在檢查更新...", "Choose a model before saving...": "儲存前請選擇一個模型...", "Chunk Overlap": "區塊重疊", "Chunk Params": "區塊參數", "Chunk Size": "區塊大小", "Ciphers": "加密方式", "Citation": "引用", "Clear memory": "清除記憶", "click here": "點選這裡", "Click here for filter guides.": "點選這裡查看篩選器指南。", "Click here for help.": "點選這裡取得協助。", "Click here to": "點選這裡", "Click here to download user import template file.": "點選這裡下載使用者匯入範本檔案。", "Click here to learn more about faster-whisper and see the available models.": "點選這裡了解更多關於 faster-whisper 的資訊並查看可用的模型。", "Click here to select": "點選這裡選擇", "Click here to select a csv file.": "點選這裡選擇 CSV 檔案。", "Click here to select a py file.": "點選這裡選擇 Python 檔案。", "Click here to upload a workflow.json file.": "點選這裡上傳 workflow.json 檔案。", "click here.": "點選這裡。", "Click on the user role button to change a user's role.": "點選使用者角色按鈕變更使用者的角色。", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "剪貼簿寫入權限遭拒。請檢查您的瀏覽器設定，授予必要的存取權限。", "Clone": "複製", "Close": "關閉", "Code execution": "程式碼執行", "Code formatted successfully": "程式碼格式化成功", "Collection": "收藏", "Color": "顏色", "ComfyUI": "ComfyUI", "ComfyUI API Key": "ComfyUI API 金鑰", "ComfyUI Base URL": "ComfyUI 基礎 URL", "ComfyUI Base URL is required.": "需要 ComfyUI 基礎 URL。", "ComfyUI Workflow": "ComfyUI 工作流程", "ComfyUI Workflow Nodes": "ComfyUI 工作流程節點", "Command": "命令", "Completions": "補全", "Concurrent Requests": "平行請求", "Configure": "設定", "Configure Models": "設定模型", "Confirm": "確認", "Confirm Password": "確認密碼", "Confirm your action": "確認您的操作", "Confirm your new password": "確認您的新密碼", "Connections": "連線", "Contact Admin for WebUI Access": "請聯絡管理員以取得 WebUI 存取權限", "Content": "內容", "Content Extraction": "內容擷取", "Context Length": "上下文長度", "Continue Response": "繼續回應", "Continue with {{provider}}": "使用 {{provider}} 繼續", "Continue with Email": "使用 Email 繼續", "Continue with LDAP": "使用 LDAP 繼續", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "控制文字轉語音（TTS）請求中如何分割訊息文字。「標點符號」分割為句子，「段落」分割為段落，「無」則保持訊息為單一字串。", "Controls": "控制項", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text. (Default: 5.0)": "控制輸出的連貫性和多樣性之間的平衡。較低的值會產生更專注和連貫的文字。（預設：5.0）", "Copied": "已複製", "Copied shared chat URL to clipboard!": "已複製共用對話 URL 到剪貼簿！", "Copied to clipboard": "已複製到剪貼簿", "Copy": "複製", "Copy last code block": "複製最後一個程式碼區塊", "Copy last response": "複製最後一個回應", "Copy Link": "複製連結", "Copy to clipboard": "複製到剪貼簿", "Copying to clipboard was successful!": "成功複製到剪貼簿！", "Create": "建立", "Create a knowledge base": "建立知識", "Create a model": "建立模型", "Create Account": "建立帳號", "Create Admin Account": "建立管理員賬號", "Create Channel": "建立頻道", "Create Group": "建立群組", "Create Knowledge": "建立知識", "Create new key": "建立新的金鑰", "Create new secret key": "建立新的金鑰", "Created at": "建立於", "Created At": "建立於", "Created by": "建立者", "CSV Import": "CSV 匯入", "Current Model": "目前模型", "Current Password": "目前密碼", "Custom": "自訂", "Dark": "深色", "Database": "資料庫", "December": "12 月", "Default": "預設", "Default (Open AI)": "預設 (OpenAI)", "Default (SentenceTransformers)": "預設 (SentenceTransformers)", "Default Model": "預設模型", "Default model updated": "預設模型已更新", "Default Models": "預設模型", "Default permissions": "預設權限", "Default permissions updated successfully": "預設權限更新成功", "Default Prompt Suggestions": "預設提示詞建議", "Default to 389 or 636 if TLS is enabled": "如果啓用了 TLS 則預設為 389 或 636", "Default to ALL": "預設到所有", "Default User Role": "預設使用者角色", "Delete": "刪除", "Delete a model": "刪除模型", "Delete All Chats": "刪除所有對話紀錄", "Delete All Models": "刪除所有模型", "Delete chat": "刪除對話紀錄", "Delete Chat": "刪除對話紀錄", "Delete chat?": "刪除對話紀錄？", "Delete folder?": "刪除資料夾？", "Delete function?": "刪除函式？", "Delete Message": "刪除訊息？", "Delete prompt?": "刪除提示詞？", "delete this link": "刪除此連結", "Delete tool?": "刪除工具？", "Delete User": "刪除使用者", "Deleted {{deleteModelTag}}": "已刪除 {{deleteModelTag}}", "Deleted {{name}}": "已刪除 {{name}}", "Deleted User": "刪除使用者？", "Describe your knowledge base and objectives": "描述您的知識庫和目標", "Description": "描述", "Disabled": "已停用", "Discover a function": "發掘函式", "Discover a model": "發掘模型", "Discover a prompt": "發掘提示詞", "Discover a tool": "發掘工具", "Discover wonders": "發掘奇蹟", "Discover, download, and explore custom functions": "發掘、下載及探索自訂函式", "Discover, download, and explore custom prompts": "發掘、下載及探索自訂提示詞", "Discover, download, and explore custom tools": "發掘、下載及探索自訂工具", "Discover, download, and explore model presets": "發掘、下載及探索模型預設集", "Dismissible": "可忽略", "Display": "顯示", "Display Emoji in Call": "在通話中顯示表情符號", "Display the username instead of You in the Chat": "在對話中顯示使用者名稱，而非「您」", "Displays citations in the response": "在回應中顯示引用", "Dive into knowledge": "深入知識", "Do not install functions from sources you do not fully trust.": "請勿從您無法完全信任的來源安裝函式。", "Do not install tools from sources you do not fully trust.": "請勿從您無法完全信任的來源安裝工具。", "Document": "文件", "Documentation": "文件", "Documents": "文件", "does not make any external connections, and your data stays securely on your locally hosted server.": "不會建立任何外部連線，而且您的資料會安全地儲存在您本機伺服器上。", "Don't have an account?": "還沒註冊帳號嗎？", "don't install random functions from sources you don't trust.": "請勿從您無法信任的來源安裝隨機函式。", "don't install random tools from sources you don't trust.": "請勿從您無法信任的來源安裝隨機工具。", "Done": "完成", "Download": "下載", "Download canceled": "已取消下載", "Download Database": "下載資料庫", "Drag and drop a file to upload or select a file to view": "拖放檔案以上傳或選擇檔案以檢視", "Draw": "繪製", "Drop any files here to add to the conversation": "拖拽任意檔案到此處以新增至對話", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "例如：'30s'、'10m'。有效的時間單位為 's'、'm'、'h'。", "e.g. A filter to remove profanity from text": "例如：從文字中移除髒話的篩選器", "e.g. My Filter": "例如：我的篩選器", "e.g. My Tools": "例如：我的工具", "e.g. my_filter": "例如：my_filter", "e.g. my_tools": "例如：my_tools", "e.g. Tools for performing various operations": "例如：用於執行各種操作的工具", "Edit": "編輯", "Edit Arena Model": "編輯競技模型", "Edit Channel": "編輯頻道", "Edit Connection": "編輯連線", "Edit Default Permissions": "編輯預設權限", "Edit Memory": "編輯記憶", "Edit User": "編輯使用者", "Edit User Group": "編輯使用者群組", "ElevenLabs": "ElevenLabs", "Email": "Email", "Embark on adventures": "展開探險之旅", "Embedding Batch Size": "嵌入批次大小", "Embedding Model": "嵌入模型", "Embedding Model Engine": "嵌入模型引擎", "Embedding model set to \"{{embedding_model}}\"": "嵌入模型已設定為 \"{{embedding_model}}\"", "Enable API Key": "啟用 API 金鑰", "Enable autocomplete generation for chat messages": "啟用聊天訊息的自動完成生成", "Enable Community Sharing": "啟用社群分享", "Enable Google Drive": "啟用 Google Drive", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "啟用記憶體鎖定（mlock）以防止模型資料被換出 RAM。此選項會將模型的工作頁面集鎖定在 RAM 中，確保它們不會被換出到磁碟。這可以透過避免頁面錯誤和確保快速資料存取來維持效能。", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "啟用記憶體映射（mmap）以載入模型資料。此選項允許系統使用磁碟儲存作為 RAM 的延伸，透過將磁碟檔案視為在 RAM 中來處理。這可以透過允許更快的資料存取來改善模型效能。然而，它可能無法在所有系統上正常運作，並且可能會消耗大量磁碟空間。", "Enable Message Rating": "啟用訊息評分", "Enable Mirostat sampling for controlling perplexity. (Default: 0, 0 = Disabled, 1 = Mirostat, 2 = Mirostat 2.0)": "啟用 Mirostat 採樣以控制困惑度。（預設：0，0 = 停用，1 = Mirostat，2 = Mirostat 2.0）", "Enable New Sign Ups": "允許新使用者註冊", "Enable Web Search": "啟用網頁搜尋", "Enabled": "已啟用", "Engine": "引擎", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "請確認您的 CSV 檔案包含以下 4 個欄位，並按照此順序排列：姓名、電子郵件、密碼、角色。", "Enter {{role}} message here": "在此輸入 {{role}} 訊息", "Enter a detail about yourself for your LLMs to recall": "輸入有關您的詳細資訊，讓您的大型語言模型可以回想起來", "Enter api auth string (e.g. username:password)": "輸入 API 驗證字串（例如：username:password）", "Enter Application DN": "輸入應用程式 DN", "Enter Application DN Password": "輸入應用程式 DN 密碼", "Enter Bing Search V7 Endpoint": "輸入 Bing 搜尋 V7 端點", "Enter Bing Search V7 Subscription Key": "輸入 Bing 搜尋 V7 訂閱金鑰", "Enter Brave Search API Key": "輸入 Brave 搜尋 API 金鑰", "Enter certificate path": "輸入憑證路徑", "Enter CFG Scale (e.g. 7.0)": "輸入 CFG 比例（例如：7.0）", "Enter Chunk Overlap": "輸入區塊重疊", "Enter Chunk Size": "輸入區塊大小", "Enter description": "輸入描述", "Enter Github Raw URL": "輸入 GitHub Raw URL", "Enter Google PSE API Key": "輸入 Google PSE API 金鑰", "Enter Google PSE Engine Id": "輸入 Google PSE 引擎 ID", "Enter Image Size (e.g. 512x512)": "輸入圖片大小（例如：512x512）", "Enter Jina API Key": "輸入 Jina API 金鑰", "Enter Kagi Search API Key": "輸入 Kagi 搜尋 API 金鑰", "Enter language codes": "輸入語言代碼", "Enter Model ID": "輸入模型 ID", "Enter model tag (e.g. {{modelTag}})": "輸入模型標籤（例如：{{modelTag}}）", "Enter Mojeek Search API Key": "輸入 Mojeek 搜尋 API 金鑰", "Enter name or email": "", "Enter Number of Steps (e.g. 50)": "輸入步驟數（例如：50）", "Enter proxy URL (e.g. **************************:port)": "輸入代理程式 URL（例如：**************************:port）", "Enter Sampler (e.g. Euler a)": "輸入取樣器（例如：Euler a）", "Enter Scheduler (e.g. Karras)": "輸入排程器（例如：Karras）", "Enter Score": "輸入分數", "Enter SearchApi API Key": "輸入 SearchApi API 金鑰", "Enter SearchApi Engine": "輸入 SearchApi 引擎", "Enter Searxng Query URL": "輸入 SearXNG 查詢 URL", "Enter Seed": "輸入種子值", "Enter Serper API Key": "輸入 Serper API 金鑰", "Enter Serply API Key": "輸入 Serply API 金鑰", "Enter Serpstack API Key": "輸入 Serpstack API 金鑰", "Enter server host": "輸入伺服器主機", "Enter server label": "輸入伺服器標籤", "Enter server port": "輸入伺服器連接埠", "Enter stop sequence": "輸入停止序列", "Enter system prompt": "輸入系統提示詞", "Enter Tavily API Key": "輸入 Tavily API 金鑰", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "請輸入您 WebUI 的公開 URL。此 URL 將用於在通知中產生連結。", "Enter Tika Server URL": "輸入 Tika 伺服器 URL", "Enter Top K": "輸入 Top K 值", "Enter URL (e.g. http://127.0.0.1:7860/)": "輸入 URL（例如：http://127.0.0.1:7860/）", "Enter URL (e.g. http://localhost:11434)": "輸入 URL（例如：http://localhost:11434）", "Enter your current password": "輸入您的當前密碼", "Enter Your Email": "輸入您的電子郵件", "Enter Your Full Name": "輸入您的全名", "Enter your message": "輸入您的訊息", "Enter your new password": "輸入您的新密碼", "Enter Your Password": "輸入您的密碼", "Enter your prompt": "", "Enter Your Role": "輸入您的角色", "Enter Your Username": "輸入您的使用者名稱", "Enter your webhook URL": "輸入您的 webhook URL", "Error": "錯誤", "ERROR": "錯誤", "Error accessing Google Drive: {{error}}": "存取 Google Drive 時發生錯誤：{{error}}", "Error uploading file: {{error}}": "上傳檔案時發生錯誤：{{error}}", "Evaluations": "評估", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "範例：(&(objectClass=inetOrgPerson)(uid=%s))", "Example: ALL": "範例：ALL", "Example: ou=users,dc=foo,dc=example": "範例：ou=users,dc=foo,dc=example", "Example: sAMAccountName or uid or userPrincipalName": "範例：sAMAccountName 或 uid 或 userPrincipalName", "Exclude": "排除", "Experimental": "實驗性功能", "Explore the cosmos": "探索宇宙", "Export": "匯出", "Export All Archived Chats": "匯出所有已封存的對話", "Export All Chats (All Users)": "匯出所有對話紀錄（所有使用者）", "Export chat (.json)": "匯出對話紀錄（.json）", "Export Chats": "匯出對話紀錄", "Export Config to JSON File": "將設定匯出為 JSON 檔案", "Export Functions": "匯出函式", "Export Models": "匯出模型", "Export Presets": "匯出預設集", "Export Prompts": "匯出提示詞", "Export to CSV": "匯出為 CSV", "Export Tools": "匯出工具", "External Models": "外部模型", "Extremely bad": "", "Failed to add file.": "新增檔案失敗。", "Failed to create API Key.": "建立 API 金鑰失敗。", "Failed to read clipboard contents": "讀取剪貼簿內容失敗", "Failed to save models configuration": "儲存模型設定失敗", "Failed to update settings": "更新設定失敗", "February": "2 月", "Feedback History": "回饋歷史", "Feedbacks": "回饋", "File": "檔案", "File added successfully.": "檔案新增成功。", "File content updated successfully.": "檔案內容更新成功。", "File Mode": "檔案模式", "File not found.": "找不到檔案。", "File removed successfully.": "成功移除檔案。", "File size should not exceed {{maxSize}} MB.": "檔案大小不應超過 {{maxSize}} MB。", "File uploaded successfully": "檔案上傳成功", "Files": "檔案", "Filter is now globally disabled": "篩選器現在已全域停用", "Filter is now globally enabled": "篩選器現在已全域啟用", "Filters": "篩選器", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "偵測到指紋偽造：無法使用姓名縮寫作為大頭貼。將預設為預設個人檔案圖片。", "Fluidly stream large external response chunks": "流暢地串流大型外部回應區塊", "Focus chat input": "聚焦對話輸入", "Folder deleted successfully": "資料夾刪除成功", "Folder name cannot be empty": "資料夾名稱不能為空", "Folder name cannot be empty.": "資料夾名稱不能為空。", "Folder name updated successfully": "資料夾名稱更新成功", "Forge new paths": "開創新路徑", "Form": "表單", "Format your variables using brackets like this:": "使用方括號格式化您的變數，如下所示：", "Frequency Penalty": "頻率懲罰", "Function": "函式", "Function created successfully": "成功建立函式", "Function deleted successfully": "成功刪除函式", "Function Description": "函式描述", "Function ID": "函式 ID", "Function is now globally disabled": "現在已在全域停用函式", "Function is now globally enabled": "現在已在全域啟用函式", "Function Name": "函式名稱", "Function updated successfully": "成功更新函式", "Functions": "函式", "Functions allow arbitrary code execution": "函式允許執行任意程式碼", "Functions allow arbitrary code execution.": "函式允許執行任意程式碼。", "Functions imported successfully": "成功匯入函式", "General": "一般", "General Settings": "一般設定", "Generate Image": "產生圖片", "Generating search query": "正在產生搜尋查詢", "Get started": "開始使用", "Get started with {{WEBUI_NAME}}": "開始使用 {{WEBUI_NAME}}", "Global": "全域", "Good Response": "良好回應", "Google Drive": "Google Drive", "Google PSE API Key": "Google PSE API 金鑰", "Google PSE Engine Id": "Google PSE 引擎 ID", "Group created successfully": "群組建立成功", "Group deleted successfully": "群組刪除成功", "Group Description": "群組描述", "Group Name": "群組名稱", "Group updated successfully": "群組更新成功", "Groups": "群組", "GSA Chat can make mistakes. Review all responses for accuracy.": "", "h:mm a": "h:mm a", "Haptic Feedback": "觸覺回饋", "Harmful or offensive": "", "has no conversations.": "沒有對話。", "Hello, {{name}}": "您好，{{name}}", "Help": "說明", "Help us create the best community leaderboard by sharing your feedback history!": "透過分享您的回饋歷史，幫助我們建立最佳的社群排行榜！", "Hex Color": "Hex 顔色", "Hex Color - Leave empty for default color": "Hex 顔色 —— 留空以使用預設顔色", "Hide": "隱藏", "Host": "主機", "How can I help you today?": "今天我能為您做些什麼？", "How would you rate this response?": "您如何評價此回應？", "Hybrid Search": "混合搜尋", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "我確認已閱讀並理解我的操作所帶來的影響。我了解執行任意程式碼的相關風險，並已驗證來源的可信度。", "ID": "ID", "Ignite curiosity": "點燃好奇心", "Image Compression": "圖片壓縮", "Image Generation (Experimental)": "圖片生成（實驗性功能）", "Image Generation Engine": "圖片生成引擎", "Image Max Compression Size": "圖片最大壓縮大小", "Image Settings": "圖片設定", "Images": "圖片", "Import Chats": "匯入對話紀錄", "Import Config from JSON File": "從 JSON 檔案匯入設定", "Import Functions": "匯入函式", "Import Models": "匯入模型", "Import Presets": "匯入預設集", "Import Prompts": "匯入提示詞", "Import Tools": "匯入工具", "Include": "包含", "Include `--api-auth` flag when running stable-diffusion-webui": "執行 stable-diffusion-webui 時包含 `--api-auth` 參數", "Include `--api` flag when running stable-diffusion-webui": "執行 stable-diffusion-webui 時包含 `--api` 參數", "Incomplete response": "", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive. (Default: 0.1)": "影響演算法對回饋的反應速度。較低的學習率會導致調整速度較慢，而較高的學習率會使演算法更快回應。（預設：0.1）", "Info": "資訊", "Input commands": "輸入命令", "Install from Github URL": "從 GitHub URL 安裝", "Instant Auto-Send After Voice Transcription": "語音轉錄後立即自動傳送", "Interface": "介面", "Invalid file format.": "無效檔案格式。", "Invalid Tag": "無效標籤", "is typing...": "正在輸入……", "January": "1 月", "Jina API Key": "Jina API 金鑰", "join our Discord for help.": "加入我們的 Discord 以尋求協助。", "JSON": "JSON", "JSON Preview": "JSON 預覽", "July": "7 月", "June": "6 月", "JWT Expiration": "JWT 過期時間", "JWT Token": "JWT Token", "Kagi Search API Key": "Kagi 搜尋 API 金鑰", "Keep Alive": "保持連線", "Key": "金鑰", "Keyboard shortcuts": "鍵盤快捷鍵", "Knowledge": "知識", "Knowledge Access": "知識存取", "Knowledge created successfully.": "知識建立成功。", "Knowledge deleted successfully.": "知識刪除成功。", "Knowledge reset successfully.": "知識重設成功。", "Knowledge updated successfully": "知識更新成功", "Label": "標籤", "Landing Page Mode": "首頁模式", "Language": "語言", "Last Active": "上次活動時間", "Last Modified": "上次修改時間", "Last reply": "", "Latest users": "", "LDAP": "LDAP", "LDAP server updated": "LDAP 伺服器已更新", "Leaderboard": "排行榜", "Leave empty for unlimited": "留空表示無限制", "Leave empty to include all models from \"{{URL}}/api/tags\" endpoint": "留空以包含來自 \"{{URL}}/api/tags\" 端點的所有模型", "Leave empty to include all models from \"{{URL}}/models\" endpoint": "留空以包含來自 \"{{URL}}/models\" 端點的所有模型", "Leave empty to include all models or select specific models": "留空以包含所有模型或選擇特定模型", "Leave empty to use the default prompt, or enter a custom prompt": "留空以使用預設提示詞，或輸入自訂提示詞", "Light": "淺色", "Listening...": "正在聆聽...", "Local": "本機", "Local Models": "本機模型", "Lost": "已遺失", "LTR": "從左到右", "Made by OpenWebUI Community": "由 OpenWebUI 社群製作", "Make sure to enclose them with": "請務必將它們放在", "Make sure to export a workflow.json file as API format from ComfyUI.": "請確保從 ComfyUI 匯出 workflow.json 檔案為 API 格式。", "Manage": "管理", "Manage Arena Models": "管理競技模型", "Manage Ollama": "管理 <PERSON><PERSON>ma", "Manage Ollama API Connections": "管理 Ollama API 連線", "Manage OpenAI API Connections": "管理 OpenAI API 連線", "Manage Pipelines": "管理管線", "March": "3 月", "Max Tokens (num_predict)": "最大 token 數（num_predict）", "Max Upload Count": "最大上傳數量", "Max Upload Size": "最大上傳大小", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "最多可同時下載 3 個模型。請稍後再試。", "May": "5 月", "Memories accessible by LLMs will be shown here.": "可被大型語言模型存取的記憶將顯示在這裡。", "Memory": "記憶", "Memory added successfully": "成功新增記憶", "Memory cleared successfully": "成功清除記憶", "Memory deleted successfully": "成功刪除記憶", "Memory updated successfully": "成功更新記憶", "Merge Responses": "合併回應", "Message rating should be enabled to use this feature": "需要啟用訊息評分才能使用此功能", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "建立連結後傳送的訊息不會被分享。擁有網址的使用者可檢視分享的對話內容。", "Min P": "最小 P 值", "Minimum Score": "最低分數", "Mirostat": "Mirostat", "Mirostat Eta": "Mirostat Eta", "Mirostat Tau": "Mirostat Tau", "MMMM DD, YYYY": "YYYY 年 MMMM DD 日", "MMMM DD, YYYY HH:mm": "YYYY 年 MMMM DD 日 HH:mm", "MMMM DD, YYYY hh:mm:ss A": "YYYY 年 MMMM DD 日 hh:mm:ss A", "Model": "模型", "Model '{{modelName}}' has been successfully downloaded.": "模型「{{modelName}}」已成功下載。", "Model '{{modelTag}}' is already in queue for downloading.": "模型「{{modelTag}}」已在下載佇列中。", "Model {{modelId}} not found": "找不到模型 {{modelId}}", "Model {{modelName}} is not vision capable": "模型 {{modelName}} 不具備視覺能力", "Model {{name}} is now {{status}}": "模型 {{name}} 現在狀態為 {{status}}", "Model accepts image inputs": "模型接受影像輸入", "Model created successfully!": "成功建立模型！", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "偵測到模型檔案系統路徑。更新需要模型簡稱，因此無法繼續。", "Model Filtering": "模型篩選", "Model ID": "模型 ID", "Model IDs": "模型 IDs", "Model Name": "模型名稱", "Model not selected": "未選取模型", "Model Params": "模型參數", "Model Permissions": "模型權限", "Model updated successfully": "成功更新模型", "Modelfile Content": "模型檔案內容", "Models": "模型", "Models Access": "模型存取", "Models configuration saved successfully": "模型設定保存成功", "Mojeek Search API Key": "Mojeek 搜尋 API 金鑰", "more": "更多", "More": "更多", "Name": "名稱", "Name your knowledge base": "命名您的知識庫", "New Chat": "新增對話", "New folder": "新增資料夾", "New Password": "新密碼", "new-channel": "new-channel", "No content found": "找不到內容", "No content to speak": "無可朗讀的內容", "No distance available": "無可用距離", "No feedbacks found": "找不到回饋", "No file selected": "未選取檔案", "No files found.": "找不到檔案。", "No groups with access, add a group to grant access": "沒有具有存取權限的群組，新增群組以授予存取權限", "No HTML, CSS, or JavaScript content found.": "找不到 HTML、CSS 或 JavaScript 內容。", "No knowledge found": "找不到知識", "No model IDs": "沒有任何模型 ID", "No models found": "找不到模型", "No models selected": "未選取模型", "No results found": "找不到任何結果", "No search query generated": "未產生搜尋查詢", "No source available": "無可用源", "No users were found.": "找不到任何使用者", "No valves to update": "無閥門可更新", "None": "無", "Not accurate": "", "Not relevant": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "注意：如果您設定了最低分數，則搜尋只會回傳分數大於或等於最低分數的文件。", "Notes": "注意", "Notification Sound": "通知聲音", "Notification Webhook": "通知 Webhook", "Notifications": "通知", "November": "11 月", "num_gpu (Ollama)": "num_gpu (Ollama)", "num_thread (Ollama)": "num_thread (Ollama)", "OAuth ID": "OAuth ID", "October": "10 月", "Off": "關閉", "Okay, Let's Go!": "好的，我們開始吧！", "OLED Dark": "OLED 深色", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API disabled": "Ollama API 已停用", "Ollama API settings updated": "Ollama API 設定已更新", "Ollama Version": "Ollama 版本", "On": "開啟", "Only alphanumeric characters and hyphens are allowed": "只允許使用英文字母、數字和連字號", "Only alphanumeric characters and hyphens are allowed in the command string.": "命令字串中只允許使用英文字母、數字和連字號。", "Only collections can be edited, create a new knowledge base to edit/add documents.": "只能編輯集合，請建立新的知識以編輯或新增文件。", "Only select users and groups with permission can access": "只有具有權限的選定使用者和群組可以存取", "Oops! Looks like the URL is invalid. Please double-check and try again.": "哎呀！這個 URL 似乎無效。請仔細檢查並再試一次。", "Oops! There are files still uploading. Please wait for the upload to complete.": "哎呀！還有檔案正在上傳。請等候上傳完畢。", "Oops! There was an error in the previous response.": "哎呀！之前的回應有一處錯誤。", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "哎呀！您使用了不支援的方法（僅限前端）。請從後端提供 WebUI。", "Open in full screen": "全螢幕開啟", "Open new chat": "開啟新的對話", "Open WebUI uses faster-whisper internally.": "Open WebUI 使用内部 faster-whisper。", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "Open WebUI 使用 SpeechT5 和 CMU Arctic 說話者嵌入。", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "Open WebUI 版本 (v{{OPEN_WEBUI_VERSION}}) 低於所需版本 (v{{REQUIRED_VERSION}})", "OpenAI": "OpenAI", "OpenAI API": "OpenAI API", "OpenAI API Config": "OpenAI API 設定", "OpenAI API Key is required.": "需要 OpenAI API 金鑰。", "OpenAI API settings updated": "OpenAI API 設定已更新", "OpenAI URL/Key required.": "需要 OpenAI URL或金鑰。", "or": "或", "Organize your users": "組織您的使用者", "OUTPUT": "輸出", "Output format": "輸出格式", "Overview": "概覽", "page": "頁面", "Password": "密碼", "Paste Large Text as File": "將大型文字以檔案貼上", "PDF document (.pdf)": "PDF 文件 (.pdf)", "PDF Extract Images (OCR)": "PDF 影像擷取（OCR 光學文字辨識）", "pending": "待處理", "Permission denied when accessing media devices": "存取媒體裝置時權限遭拒", "Permission denied when accessing microphone": "存取麥克風時權限遭拒", "Permission denied when accessing microphone: {{error}}": "存取麥克風時權限遭拒：{{error}}", "Permissions": "權限", "Personalization": "個人化", "Pin": "釘選", "Pinned": "已釘選", "Pioneer insights": "先驅見解", "Pipeline deleted successfully": "成功刪除管線", "Pipeline downloaded successfully": "成功下載管線", "Pipelines": "管線", "Pipelines Not Detected": "未偵測到管線", "Pipelines Valves": "管線閥門", "Plain text (.txt)": "純文字 (.txt)", "Playground": "遊樂場", "Please carefully review the following warnings:": "請仔細閱讀以下警告：", "Please enter a prompt": "請輸入提示詞", "Please fill in all fields.": "請填寫所有欄位。", "Please select a model first.": "請先選擇型號。", "Port": "連接埠", "Prefix ID": "前綴 ID", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "前綴 ID 用於透過為模型 ID 新增前綴以避免與其他連線衝突 - 留空以停用", "Previous 30 days": "過去 30 天", "Previous 7 days": "過去 7 天", "Profile Image": "個人檔案圖片", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "提示詞（例如：告訴我關於羅馬帝國的一些趣事）", "Prompt Content": "提示詞內容", "Prompt created successfully": "提示詞建立成功", "Prompt suggestions": "提示詞建議", "Prompt updated successfully": "提示詞更新成功", "Prompts": "提示詞", "Prompts Access": "提示詞存取", "Provide any specific details": "", "Proxy URL": "代理網址", "Pull \"{{searchValue}}\" from Ollama.com": "從 Ollama.com 下載「{{searchValue}}」", "Pull a model from Ollama.com": "從 Ollama.com 下載模型", "Query Generation Prompt": "查詢生成提示詞", "Query Params": "查詢參數", "RAG Template": "RAG 範本", "Rating": "評分", "Re-rank models by topic similarity": "根據主題相似度重新排序模型", "Read Aloud": "大聲朗讀", "Record voice": "錄音", "Redirecting you to OpenWebUI Community": "正在將您重導向至 OpenWebUI 社群", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative. (Default: 40)": "降低產生無意義內容的機率。較高的值（例如 100）會給出更多樣化的答案，而較低的值（例如 10）會更保守。（預設：40）", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "以「使用者」稱呼自己（例如：「使用者正在學習西班牙文」）", "References from": "引用來源", "Refresh Token Expiration": "", "Regenerate": "重新產生", "Release Notes": "釋出説明", "Relevance": "相關性", "Remove": "移除", "Remove Model": "移除模型", "Rename": "重新命名", "Reorder Models": "重新排序模型", "Repeat Last N": "重複最後 N 個", "Reply in Thread": "", "Request Mode": "請求模式", "Reranking Model": "重新排序模型", "Reranking model disabled": "已停用重新排序模型", "Reranking model set to \"{{reranking_model}}\"": "重新排序模型已設定為 \"{{reranking_model}}\"", "Reset": "重設", "Reset All Models": "重設所有模型", "Reset Upload Directory": "重設上傳目錄", "Reset Vector Storage/Knowledge": "重設向量儲存或知識", "Reset view": "重設視圖", "Response generation stopped": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "無法啟用回應通知，因為網站權限已遭拒。請前往瀏覽器設定以授予必要存取權限。", "Response splitting": "回應分割", "Result": "結果", "Retrieval Query Generation": "檢索查詢生成", "Rich Text Input for Chat": "使用富文本輸入對話", "RK": "RK", "Role": "角色", "Rosé Pine": "玫瑰松", "Rosé Pine Dawn": "黎明玫瑰松", "RTL": "從右到左", "Run": "執行", "Running": "運作中", "Save": "儲存", "Save & Create": "儲存並建立", "Save & Update": "儲存並更新", "Save As Copy": "另存為副本", "Save Tag": "儲存標籤", "Saved": "已儲存", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "不再支援直接將對話紀錄儲存到您的瀏覽器儲存空間。請點選下方按鈕來下載並刪除您的對話紀錄。別擔心，您可以透過以下方式輕鬆地將對話紀錄重新匯入後端", "Scroll to bottom when switching between branches": "切換分支時捲動到底端", "Search": "搜尋", "Search a model": "搜尋模型", "Search Base": "搜尋基礎", "Search Chats": "搜尋對話", "Search Collection": "搜尋集合", "Search Filters": "搜尋篩選器", "search for tags": "搜尋標籤", "Search Functions": "搜尋函式", "Search Knowledge": "搜尋知識庫", "Search Models": "搜尋模型", "Search options": "搜尋選項", "Search Prompts": "搜尋提示詞", "Search Result Count": "搜尋結果數量", "Search the web": "搜尋網頁", "Search Tools": "搜尋工具", "Search users": "", "SearchApi API Key": "SearchApi API 金鑰", "SearchApi Engine": "SearchApi 引擎", "Searched {{count}} sites": "搜尋到 {{count}} 個站點", "Searching \"{{searchQuery}}\"": "正在搜尋「{{searchQuery}}」", "Searching Knowledge for \"{{searchQuery}}\"": "正在搜尋知識庫中的「{{searchQuery}}」", "Searxng Query URL": "Searxng 查詢 URL", "See readme.md for instructions": "檢視 readme.md 以取得說明", "See what's new": "檢視新功能", "Seed": "種子值", "Select a base model": "選擇基礎模型", "Select a engine": "選擇引擎", "Select a function": "選擇函式", "Select a group": "選擇群組", "Select a model": "選擇模型", "Select a pipeline": "選擇管線", "Select a pipeline url": "選擇管線 URL", "Select a tool": "選擇工具", "Select Engine": "選擇引擎", "Select Knowledge": "選擇知識庫", "Select model": "選擇模型", "Select only one model to call": "僅選擇一個模型來呼叫", "Selected model(s) do not support image inputs": "選取的模型不支援圖片輸入", "Semantic distance to query": "與查詢的語義距離", "Send": "傳送", "Send a message": "", "Send a Message": "傳送訊息", "Send message": "傳送訊息", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "在請求中傳送 `stream_options: { include_usage: true }`。\n設定後，支援的提供者將在回應中回傳權杖使用資訊。", "September": "9 月", "Serper API Key": "Serper API 金鑰", "Serply API Key": "Serply API 金鑰", "Serpstack API Key": "Serpstack API 金鑰", "Server connection verified": "伺服器連線已驗證", "Set as default": "設為預設", "Set CFG Scale": "設定 CFG 比例", "Set Default Model": "設定預設模型", "Set embedding model": "設定嵌入模型", "Set embedding model (e.g. {{model}})": "設定嵌入模型（例如：{{model}}）", "Set Image Size": "設定圖片大小", "Set reranking model (e.g. {{model}})": "設定重新排序模型（例如：{{model}}）", "Set Sampler": "設定取樣器", "Set Scheduler": "設定排程器", "Set Steps": "設定步數", "Set Task Model": "設定任務模型", "Set the number of GPU devices used for computation. This option controls how many GPU devices (if available) are used to process incoming requests. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "設定用於計算的 GPU 裝置數量。此選項控制使用多少個 GPU 裝置（如果可用）來處理傳入的請求。增加此值可以顯著提升針對 GPU 加速優化的模型效能，但也可能消耗更多電力和 GPU 資源。", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "設定用於計算的工作執行緒數量。此選項控制使用多少執行緒來同時處理傳入的請求。增加此值可以在高併發工作負載下提升效能，但也可能消耗更多 CPU 資源。", "Set Voice": "設定語音", "Set whisper model": "設定 whisper 模型", "Sets how far back for the model to look back to prevent repetition. (Default: 64, 0 = disabled, -1 = num_ctx)": "設定模型向後查看以防止重複的距離。（預設：64，0 = 停用，-1 = num_ctx）", "Sets how strongly to penalize repetitions. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. (Default: 1.1)": "設定懲罰重複的強度。較高的值（例如 1.5）會更強烈懲罰重複，而較低的值（例如 0.9）會更寬容。（預設：1.1）", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt. (Default: random)": "設定用於生成的隨機數種子。將其設定為特定數字會使模型對相同的提示詞產生相同的文字。（預設：隨機）", "Sets the size of the context window used to generate the next token. (Default: 2048)": "設定用於生成下一個 token 的上下文視窗大小。（預設：2048）", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "設定要使用的停止序列。當遇到此模式時，大型語言模型將停止生成文字並返回。可以在模型檔案中指定多個單獨的停止參數來設定多個停止模式。", "Settings": "設定", "Settings saved successfully!": "設定已成功儲存！", "Share": "分享", "Share Chat": "分享對話", "Share to OpenWebUI Community": "分享到 OpenWebUI 社群", "Show": "顯示", "Show \"What's New\" modal on login": "登入時顯示「新功能」對話框", "Show Admin Details in Account Pending Overlay": "在帳號待審覆蓋層中顯示管理員詳細資訊", "Show shortcuts": "顯示快捷鍵", "Show your support!": "表達您的支持！", "Sign in": "登入", "Sign in to {{WEBUI_NAME}}": "登入 {{WEBUI_NAME}}", "Sign in to {{WEBUI_NAME}} with LDAP": "以 LDAP 登入 {{WEBUI_NAME}}", "Sign Out": "登出", "Sign up": "註冊", "Sign up to {{WEBUI_NAME}}": "註冊 {{WEBUI_NAME}}", "Signing in to {{WEBUI_NAME}}": "正在登入 {{WEBUI_NAME}}", "sk-1234": "sk-1234", "Source": "來源", "Speech Playback Speed": "語音播放速度", "Speech recognition error: {{error}}": "語音辨識錯誤：{{error}}", "Speech-to-Text Engine": "語音轉文字 (STT) 引擎", "Stop": "停止", "Stop Sequence": "停止序列", "Stream Chat Response": "流式對話回應", "STT Model": "語音轉文字 (STT) 模型", "STT Settings": "語音轉文字 (STT) 設定", "Success": "成功", "Successfully updated.": "更新成功。", "Suggested prompts to get you started": "", "Support": "支援", "Support this plugin:": "支持這個外掛：", "Sync directory": "同步目錄", "System": "系統", "System Instructions": "系統指令", "System Prompt": "系統提示詞", "Tags Generation": "標籤生成", "Tags Generation Prompt": "標籤生成提示詞", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting. (default: 1)": "使用無尾採樣來減少較不可能的 token 對輸出的影響。較高的值（例如 2.0）會減少更多影響，而值為 1.0 則停用此設定。（預設：1）", "Tap to interrupt": "點選以中斷", "Tavily API Key": "Tavily API 金鑰", "Temperature": "溫度", "Template": "範本", "Temporary Chat": "臨時對話", "Text Splitter": "文字分割器", "Text-to-Speech Engine": "文字轉語音引擎", "Tfs Z": "Tfs Z", "Thanks for your feedback!": "感謝您的回饋！", "The Application Account DN you bind with for search": "您綁定用於搜尋的應用程式帳號 DN", "The base to search for users": "搜尋使用者的基礎", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.  (Default: 512)": "批次大小決定一次處理多少文字請求。較高的批次大小可以提高模型的效能和速度，但也需要更多記憶體。（預設：512）", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "這個外掛背後的開發者是來自社群的熱情志願者。如果您覺得這個外掛很有幫助，請考慮為其開發做出貢獻。", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "評估排行榜基於 Elo 評分系統，並即時更新。", "The LDAP attribute that maps to the username that users use to sign in.": "映射到使用者用於登入的使用者名稱的 LDAP 屬性。", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "排行榜目前處於測試階段，我們可能會在改進演算法時調整評分計算方式。", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "檔案大小上限（MB）。如果檔案大小超過此限制，檔案將不會被上傳。", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "對話中一次可使用的最大檔案數量。如果檔案數量超過此限制，檔案將不會被上傳。", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "分數應該是介於 0.0（0%）和 1.0（100%）之間的值。", "The temperature of the model. Increasing the temperature will make the model answer more creatively. (Default: 0.8)": "模型的溫度。提高溫度會使模型回答更具創意。（預設：0.8）", "Theme": "主題", "Thinking...": "正在思考...", "This action cannot be undone. Do you wish to continue?": "此操作無法復原。您確定要繼續進行嗎？", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "這確保您寶貴的對話會安全地儲存到您的後端資料庫。謝謝！", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "這是一個實驗性功能，它可能無法如預期運作，並且可能會隨時變更。", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics. (Default: 24)": "此選項控制重新整理上下文時保留多少 token。例如，如果設定為 2，則會保留對話上下文的最後 2 個 token。保留上下文可以幫助維持對話的連續性，但可能會降低回應新主題的能力。（預設：24）", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.  (Default: 128)": "此選項設定模型可以在其回應中生成的最大 token 數量。增加此限制允許模型提供更長的答案，但也可能增加產生無用或不相關內容的可能性。（預設：128）", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "此選項將刪除集合中的所有現有檔案，並用新上傳的檔案取代它們。", "This response was generated by \"{{model}}\"": "此回應由「{{model}}」生成", "This will delete": "這將會刪除", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "這將會刪除 <strong>{{NAME}}</strong> 和<strong>其所有內容</strong>。", "This will delete all models including custom models": "這將刪除所有模型，包括自訂模型", "This will delete all models including custom models and cannot be undone.": "這將刪除所有模型，包括自訂模型，且無法復原。", "This will reset the knowledge base and sync all files. Do you wish to continue?": "這將重設知識庫並同步所有檔案。您確定要繼續嗎？", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "需要 Tika 伺服器 URL。", "Tiktoken": "Tiktoken", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "提示：在每次替換後按下對話輸入框中的 Tab 鍵，即可連續更新多個變數欄位。", "Title": "標題", "Title (e.g. Tell me a fun fact)": "標題（例如：告訴我一個有趣的事實）", "Title Auto-Generation": "自動產生標題", "Title cannot be an empty string.": "標題不能是空字串。", "Title Generation Prompt": "自動產生標題的提示詞", "TLS": "TLS", "To access the available model names for downloading,": "若要存取可供下載的模型名稱，", "To access the GGUF models available for downloading,": "若要存取可供下載的 GGUF 模型，", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "若要存取 WebUI，請聯絡管理員。管理員可以從管理面板管理使用者狀態。", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "若要在此處附加知識庫，請先將它們新增到「知識」工作區。", "To learn more about available endpoints, visit our documentation.": "若要進一步了解可用的端點，請參閱我們的文件。", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "為了保護您的隱私，只會分享您回饋中的評分、模型 ID、標籤和中繼資料 —— 您的對話紀錄仍然是私密的，不會被包含在內。", "To select actions here, add them to the \"Functions\" workspace first.": "若要在此選擇動作，請先將它們新增到「函式」工作區。", "To select filters here, add them to the \"Functions\" workspace first.": "若要在此選擇篩選器，請先將它們新增到「函式」工作區。", "To select toolkits here, add them to the \"Tools\" workspace first.": "若要在此選擇工具包，請先將它們新增到「工具」工作區。", "Toast notifications for new updates": "快顯通知新的更新", "Today": "今天", "Toggle settings": "切換設定", "Toggle sidebar": "切換側邊欄", "Token": "Token", "Tokens To Keep On Context Refresh (num_keep)": "上下文重新整理時要保留的 token 數 (num_keep)", "Tool created successfully": "成功建立工具", "Tool deleted successfully": "成功刪除工具", "Tool Description": "工具描述", "Tool ID": "工具 ID", "Tool imported successfully": "成功匯入工具", "Tool Name": "工具名稱", "Tool updated successfully": "成功更新工具", "Tools": "工具", "Tools Access": "工具存取", "Tools are a function calling system with arbitrary code execution": "工具是一個具有任意程式碼執行功能的函式呼叫系統", "Tools have a function calling system that allows arbitrary code execution": "工具具有允許執行任意程式碼的函式呼叫系統", "Tools have a function calling system that allows arbitrary code execution.": "工具具有允許執行任意程式碼的函式呼叫系統。", "Top K": "Top K", "Top P": "Top P", "Transformers": "Transformers", "Trouble accessing Ollama?": "存取 Ollama 時遇到問題？", "TTS Model": "文字轉語音 (TTS) 模型", "TTS Settings": "文字轉語音 (TTS) 設定", "TTS Voice": "文字轉語音 (TTS) 聲音", "Type": "類型", "Type Hugging Face Resolve (Download) URL": "輸入 Hugging Face 的解析（下載）URL", "Uh-oh! There was an issue with the response.": "哎呀！回應出了點問題。", "UI": "使用者介面", "Unarchive All": "解除封存全部", "Unarchive All Archived Chats": "解除封存全部已封存對話", "Unarchive Chat": "解除封存對話", "Unlock mysteries": "解鎖謎題", "Unpin": "取消釘選", "Unravel secrets": "揭開秘密", "Untagged": "取消標簽的", "Update": "更新", "Update and Copy Link": "更新並複製連結", "Update for the latest features and improvements.": "更新以獲得最新功能和改進。", "Update password": "更新密碼", "Updated": "已更新", "Updated at": "更新於", "Updated At": "更新於", "Upload": "上傳", "Upload a GGUF model": "上傳 GGUF 模型", "Upload directory": "上傳目錄", "Upload files": "上傳檔案", "Upload Files": "上傳檔案", "Upload Pipeline": "上傳管線", "Upload Progress": "上傳進度", "URL": "URL", "URL Mode": "URL 模式", "Use '#' in the prompt input to load and include your knowledge.": "在提示詞輸入中使用 '#' 來載入並包含您的知識。", "Use Gravatar": "使用 Gravatar", "Use groups to group your users and assign permissions.": "使用群組來組織您的使用者並分配權限。", "Use Initials": "使用姓名縮寫", "use_mlock (Ollama)": "使用 mlock (Ollama)", "use_mmap (Ollama)": "使用 mmap (Ollama)", "user": "使用者", "User": "使用者", "User location successfully retrieved.": "成功取得使用者位置。", "Username": "使用者名稱", "Users": "使用者", "Using the default arena model with all models. Click the plus button to add custom models.": "正在使用預設競技模型與所有模型。點選加號按鈕以新增自訂模型。", "Utilize": "使用", "Valid time units:": "有效的時間單位：", "Valves": "閥門", "Valves updated": "閥門已更新", "Valves updated successfully": "閥門更新成功", "variable": "變數", "variable to have them replaced with clipboard content.": "變數，以便將其替換為剪貼簿內容。", "Version": "版本", "Version {{selectedVersion}} of {{totalVersions}}": "第 {{selectedVersion}} 版，共 {{totalVersions}} 版", "Very bad": "", "View Replies": "", "Visibility": "可見性", "Voice": "語音", "Voice Input": "語音輸入", "Warning": "警告", "Warning:": "警告：", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "警告：啟用此功能將允許使用者在伺服器上上傳任意程式碼。", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "警告：如果您更新或更改嵌入模型，您將需要重新匯入所有文件。", "Web": "網頁", "Web API": "網頁 API", "Web Loader Settings": "網頁載入器設定", "Web Search": "網頁搜尋", "Web Search Engine": "網頁搜尋引擎", "Web Search Query Generation": "網頁搜尋查詢生成", "Webhook URL": "Webhook URL", "WebUI Settings": "WebUI 設定", "WebUI URL": "WebUI URL", "WebUI will make requests to \"{{url}}/api/chat\"": "WebUI 將向 \"{{url}}/api/chat\" 發送請求", "WebUI will make requests to \"{{url}}/chat/completions\"": "WebUI 將向 \"{{url}}/chat/completions\" 發送請求", "What are you trying to achieve?": "您正在試圖完成什麽？", "What are you working on?": "您正在工作什麽？", "What didn't you like about this response?": "", "What’s New in": "新功能", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "啟用時，模型將即時回應每個對話訊息，在使用者傳送訊息後立即生成回應。此模式適用於即時對話應用程式，但在較慢的硬體上可能會影響效能。", "wherever you are": "無論您在何處", "Whisper (Local)": "Whisper（本機）", "Widescreen Mode": "寬螢幕模式", "Won": "獲勝", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text. (Default: 0.9)": "與 top-k 一起運作。較高的值（例如 0.95）會產生更多樣化的文字，而較低的值（例如 0.5）會產生更專注和保守的文字。（預設：0.9）", "Workspace": "工作區", "Workspace Permissions": "工作區權限", "Write a prompt suggestion (e.g. Who are you?)": "撰寫提示詞建議（例如：你是誰？）", "Write a summary in 50 words that summarizes [topic or keyword].": "用 50 字寫一篇總結 [主題或關鍵字] 的摘要。", "Write something...": "寫一些什麽...", "Write your model template content here": "在此撰寫您的模型範本内容", "Yesterday": "昨天", "You": "您", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "您一次最多只能與 {{maxCount}} 個檔案進行對話。", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "您可以透過下方的「管理」按鈕新增記憶，將您與大型語言模型的互動個人化，讓它們更有幫助並更符合您的需求。", "You cannot upload an empty file.": "您無法上傳空檔案", "You have no archived conversations.": "您沒有已封存的對話。", "You have shared this chat": "您已分享此對話", "You're a helpful assistant.": "您是一位樂於助人的助手。", "Your account status is currently pending activation.": "您的帳號目前正在等待啟用。", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "您的所有貢獻將會直接交給外掛開發者；Open WebUI 不會收取任何百分比。然而，所選擇的贊助平臺可能有其自身的費用。", "Youtube": "YouTube", "Youtube Loader Settings": "YouTube 載入器設定"}