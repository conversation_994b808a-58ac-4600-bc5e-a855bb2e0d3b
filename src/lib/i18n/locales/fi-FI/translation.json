{"-1 for no limit, or a positive integer for a specific limit": "-1 rajoituksetta tai positiivinen kokonaisluku enimmäismääräksi", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' tai '-1' jottei vanhene.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(esim. `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(esim. `sh webui.sh --api`)", "(latest)": "(uusin)", "{{ models }}": "{{ mallit }}", "{{COUNT}} Replies": "", "{{user}}'s Chats": "{{user}}:n keskustelut", "{{webUIName}} Backend Required": "{{webUIName}}-backend vaaditaan", "*Prompt node ID(s) are required for image generation": "<PERSON><PERSON> luo<PERSON>en vaaditaan kehote-solmun ID(t)", "A new version (v{{LATEST_VERSION}}) is now available.": "Uusi versio (v{{LATEST_VERSION}}) on nyt saatavilla.", "A task model is used when performing tasks such as generating titles for chats and web search queries": "Tehtävämallia käytetään tehtävien suorittamiseen, kuten otsikoiden luomiseen keskusteluille ja verkkohakukyselyille", "a user": "käyttäjä", "About": "Tiet<PERSON>", "Access": "Pääsy", "Access Control": "Käyttöoikeuksien hallinta", "Accessible to all users": "Käytettävissä kaikille käyttäjille", "Account": "<PERSON><PERSON>", "Account Activation Pending": "Tilin aktivointi odo<PERSON>a", "Actions": "<PERSON><PERSON><PERSON><PERSON>", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "Aktivoi tämä komento kirjoittamalla \"/{{COMMAND}}\" chat-syötteeseen.", "Active Users": "Aktiiviset käyttäjät", "Add": "Lisää", "Add a model ID": "<PERSON><PERSON><PERSON><PERSON> mallitunnus", "Add a short description about what this model does": "Lisää lyhyt kuvaus siitä, mitä tämä malli tekee", "Add a tag": "Lisää tagi", "Add Arena Model": "Lisää Arena-malli", "Add Connection": "Lisää yhteys", "Add Content": "Lisää sisältöä", "Add content here": "", "Add custom prompt": "Lisää mukautettu kehote", "Add Files": "Lisää <PERSON>", "Add Group": "Lisää ryhmä", "Add Memory": "Lisää muistia", "Add Model": "Lisää malli", "Add Reaction": "", "Add Tag": "Lisää tagi", "Add Tags": "Lisää tageja", "Add text content": "Lisää tekstisisältöä", "Add User": "Lisää käyttäjä", "Add User Group": "Lisää käyttäjäryhmä", "Adjusting these settings will apply changes universally to all users.": "Näiden asetusten s<PERSON>äminen vaikuttaa kaikkiin kä<PERSON>äji<PERSON>.", "admin": "hallinta", "Admin": "Ylläpito", "Admin Panel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Admin Settings": "Ylläpitoasetukset", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "Ylläpitäjillä on pääsy kaikkiin työkaluihin koko ajan; käyttäjät tarvitsevat työkaluja mallille määritettynä työtilassa.", "Advanced Parameters": "Edistyneet parametrit", "Advanced Params": "Edistyneet parametrit", "All Documents": "<PERSON><PERSON><PERSON>", "All models deleted successfully": "Kai<PERSON><PERSON> mallit poistettu onnistuneesti", "Allow Chat Delete": "<PERSON><PERSON> kes<PERSON>telu<PERSON>n poisto", "Allow Chat Deletion": "<PERSON><PERSON> kes<PERSON>telu<PERSON>n poisto", "Allow Chat Edit": "<PERSON><PERSON> keskustelujen muokkaus", "Allow File Upload": "<PERSON><PERSON><PERSON><PERSON>", "Allow non-local voices": "<PERSON><PERSON> ei-paikalliset äänet", "Allow Temporary Chat": "Salli väliaikaiset keskustelut", "Allow User Location": "<PERSON><PERSON> k<PERSON>j<PERSON><PERSON> sijainti", "Allow Voice Interruption in Call": "<PERSON><PERSON> keskey<PERSON> pu<PERSON>a", "Allowed Endpoints": "", "Already have an account?": "<PERSON>ko sinulla jo tili?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out. (Default: 0.0)": "Vaihtoehto top_p:lle, jolla pyritään varmistamaan laadun ja monipuolisuuden tasapaino. Parametri p edustaa pienintä todennäköisyyttä, jolla token otetaan huomioon suhteessa todennäköisimpään tokeniin. Esimerkiksi p=0.05 ja todennäköisin token todennäköisyydellä 0.9, arvoltaan alle 0.045 olevat logit suodatetaan pois. (Oletus: 0.0)", "an assistant": "avustaja", "and": "ja", "and {{COUNT}} more": "ja {{COUNT}} muuta", "and create a new shared link.": "ja luo uusi jaettu link<PERSON>.", "API Base URL": "APIn perus-URL", "API Key": "API-avain", "API Key created.": "API-avain luotu.", "API Key Endpoint Restrictions": "", "API keys": "API-avaimet", "Application DN": "Sovelluksen DN", "Application DN Password": "Sovelluksen DN-salasana", "applies to all users with the \"user\" role": "koskee kaikkia k<PERSON>ä<PERSON>, j<PERSON><PERSON> on \"käyttäj<PERSON>\"-rooli", "April": "<PERSON><PERSON><PERSON><PERSON>", "Archive": "Arkisto", "Archive All Chats": "Arkistoi kaikki kes<PERSON>ut", "Archived Chats": "Arkistoidut keskustelut", "archived-chat-export": "arkistoitu-keskustelu-vienti", "Are you sure you want to delete this channel?": "", "Are you sure you want to delete this message?": "", "Are you sure you want to unarchive all archived chats?": "<PERSON><PERSON><PERSON><PERSON> varmasti purkaa kaikkien arkistoitujen keskustelujen arkistoinnin?", "Are you sure?": "<PERSON><PERSON><PERSON>?", "Arena Models": "Arena-mallit", "Artifacts": "Artefaktit", "Ask a question": "Kysyä kysymys", "Assistant": "Avustaja", "Attach file": "<PERSON><PERSON><PERSON>", "Attribute for Username": "Käyttäjänimi-määritämä", "Audio": "<PERSON><PERSON><PERSON>", "August": "elokuu", "Authenticate": "Todentaa", "Auto-Copy Response to Clipboard": "Kopioi vastaus automaattisesti leikepöydälle", "Auto-playback response": "Soita vastaus automaattisesti", "Autocomplete Generation": "Automaattisen täydennyksen luonti", "Autocomplete Generation Input Max Length": "Automaattisen täydennyksen syötteen enimmäispituus", "Automatic1111": "Automatic1111", "AUTOMATIC1111 Api Auth String": "AUTOMATIC1111 API:n todennusmerkkijono", "AUTOMATIC1111 Base URL": "AUTOMATIC1111-perus-URL", "AUTOMATIC1111 Base URL is required.": "AUTOMATIC1111-perus-URL vaaditaan.", "Available list": "Käytettävissä oleva lue<PERSON>lo", "available!": "saatavilla!", "Azure AI Speech": "Azure AI Speech", "Azure Region": "Azure-alue", "Back": "<PERSON><PERSON><PERSON>", "Bad": "", "Bad Response": "<PERSON><PERSON>", "Banners": "Bannerit", "Base Model (From)": "Perusmalli (alkaen)", "Batch Size (num_batch)": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> (num_batch)", "before": "ennen", "Beta": "", "BETA": "", "Bing Search V7 Endpoint": "Bing Search V7 -päätep<PERSON><PERSON> osoite", "Bing Search V7 Subscription Key": "Bing Search V7 -tilauskäyttäjäavain", "Brave Search API Key": "Brave Search API -avain", "By {{name}}": "<PERSON><PERSON><PERSON><PERSON> {{name}}", "Bypass SSL verification for Websites": "Ohita SSL-varmennus verkkosivustoille", "Call": "<PERSON><PERSON><PERSON>", "Call feature is not supported when using Web STT engine": "Soittotoimintoa ei tueta käytettäessä web-puheentunnistusmoottoria", "Camera": "<PERSON><PERSON><PERSON>", "Cancel": "Peruuta", "Capabilities": "Ominaisuuksia", "Capture": "", "Certificate Path": "Varmennepolku", "Change Password": "<PERSON><PERSON><PERSON><PERSON>", "Channel Name": "", "Channels": "", "Character": "<PERSON><PERSON><PERSON>", "Character limit for autocomplete generation input": "Automaattisen täydennyksen syötteen merkkiraja", "Chart new frontiers": "<PERSON><PERSON><PERSON><PERSON> uus<PERSON> raja<PERSON>", "Chat": "Keskustelu", "Chat Background Image": "<PERSON><PERSON><PERSON><PERSON><PERSON> taustakuva", "Chat Bubble UI": "Keskustelu-pallojen k<PERSON>yttöliittymä", "Chat Controls": "<PERSON><PERSON><PERSON><PERSON><PERSON> hallinta", "Chat direction": "Ke<PERSON><PERSON><PERSON><PERSON> su<PERSON>", "Chat Overview": "Keskustelun yleisk<PERSON>aus", "Chat Permissions": "Keskustelun k<PERSON>öoikeudet", "Chat Tags Auto-Generation": "Keskustelutunnisteiden automaattinen luonti", "Chats": "Keskustelut", "Check Again": "Tarkista uudelleen", "Check for updates": "Tarkista päivitykset", "Checking for updates...": "Tarkistetaan päivityksiä...", "Choose a model before saving...": "Valitse malli ennen tallentamista...", "Chunk Overlap": "Päällekkäisten osien määrä", "Chunk Params": "Osien parametrit", "Chunk Size": "<PERSON><PERSON><PERSON> koko", "Ciphers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Citation": "Lähdeviite", "Clear memory": "<PERSON><PERSON><PERSON><PERSON><PERSON> muisti", "click here": "klikkaa tästä", "Click here for filter guides.": "<PERSON><PERSON> k<PERSON> tästä.", "Click here for help.": "Klikkaa tästä saadaksesi apua.", "Click here to": "Klikkaa tästä", "Click here to download user import template file.": "Lataa käyttäjien tuontipohjatiedosto k<PERSON>amalla tästä.", "Click here to learn more about faster-whisper and see the available models.": "Klikkaa tästä oppiaksesi lisää faster-whisperista ja nähdäksesi saatavilla olevat mallit.", "Click here to select": "Klikkaa tästä valitaksesi", "Click here to select a csv file.": "Klikkaa tästä valitaksesi CSV-tiedosto.", "Click here to select a py file.": "Klikkaa tästä valitaksesi py-tiedosto.", "Click here to upload a workflow.json file.": "Klikkaa tästä ladataksesi workflow.json-tiedosto.", "click here.": "klikkaa tästä.", "Click on the user role button to change a user's role.": "Klikkaa käyttäjän roolipainiketta vaihtaaksesi käyttäjän roolia.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "Leikepöydälle kirjoitusoikeus evätty. Tarkista selaimesi asetukset ja myönnä tarvittavat käyttöoikeudet.", "Clone": "Kloonaa", "Close": "Sulje", "Code execution": "<PERSON><PERSON><PERSON>", "Code formatted successfully": "<PERSON><PERSON><PERSON> muoto<PERSON> on<PERSON>", "Collection": "<PERSON><PERSON><PERSON><PERSON>", "Color": "<PERSON><PERSON><PERSON>", "ComfyUI": "ComfyUI", "ComfyUI API Key": "", "ComfyUI Base URL": "ComfyUI-perus-URL", "ComfyUI Base URL is required.": "ComfyUI-perus-URL vaaditaan.", "ComfyUI Workflow": "ComfyUI-työnkulku", "ComfyUI Workflow Nodes": "ComfyUI-työnkulun solmut", "Command": "Ko<PERSON>", "Completions": "Täydennykset", "Concurrent Requests": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Configure": "Määritä", "Configure Models": "Määritä malleja", "Confirm": "Vahvista", "Confirm Password": "<PERSON><PERSON><PERSON><PERSON> sa<PERSON>", "Confirm your action": "<PERSON><PERSON><PERSON><PERSON> to<PERSON>", "Confirm your new password": "", "Connections": "<PERSON><PERSON><PERSON><PERSON>", "Contact Admin for WebUI Access": "Ota yhtey<PERSON>ä ylläpitäjään WebUI-käyttöä varten", "Content": "Sisältö", "Content Extraction": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> er<PERSON>", "Context Length": "<PERSON><PERSON><PERSON><PERSON>", "Continue Response": "<PERSON>at<PERSON> vastaus<PERSON>", "Continue with {{provider}}": "<PERSON><PERSON><PERSON> palvelulla {{provider}}", "Continue with Email": "Jatka sähköpostilla", "Continue with LDAP": "Jatka LDAP:illa", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, miten viestin teksti jaetaan puhesynteesipyyntöjä varten. 'Välimerkit' jakaa lause<PERSON>, 'kappa<PERSON><PERSON>' jakaa kappaleisiin ja 'ei mitään' pitää viestin yhtenä merkkijonona.", "Controls": "<PERSON><PERSON><PERSON><PERSON>", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text. (Default: 5.0)": "Säätelee tulosteen yhtenäisyyden ja monimuotoisuuden välistä tasapainoa. Alhaisempi arvo tuottaa keskittyneempää ja yhtenäisempää tekstiä. (Oletus: 5.0)", "Copied": "Ko<PERSON><PERSON><PERSON>", "Copied shared chat URL to clipboard!": "Jaettu keskustelulinkki kopioitu leikepöydälle!", "Copied to clipboard": "<PERSON><PERSON><PERSON><PERSON>öyd<PERSON>", "Copy": "Ko<PERSON>i", "Copy last code block": "Kopioi vii<PERSON><PERSON> k<PERSON>", "Copy last response": "Kopioi vii<PERSON><PERSON> vastaus", "Copy Link": "<PERSON><PERSON><PERSON>", "Copy to clipboard": "<PERSON><PERSON><PERSON>yd<PERSON>", "Copying to clipboard was successful!": "<PERSON><PERSON>iminen leikepöydälle onnistui!", "Create": "<PERSON><PERSON>", "Create a knowledge base": "<PERSON><PERSON>", "Create a model": "<PERSON>o malli", "Create Account": "<PERSON><PERSON> tili", "Create Admin Account": "<PERSON><PERSON>", "Create Channel": "", "Create Group": "<PERSON><PERSON>", "Create Knowledge": "<PERSON><PERSON> <PERSON><PERSON>", "Create new key": "<PERSON><PERSON> uusi avain", "Create new secret key": "<PERSON><PERSON> uusi salainen avain", "Created at": "<PERSON><PERSON><PERSON>", "Created At": "<PERSON><PERSON><PERSON>", "Created by": "Luonut", "CSV Import": "CSV-tuonti", "Current Model": "Nykyinen malli", "Current Password": "<PERSON><PERSON><PERSON><PERSON>", "Custom": "Muka<PERSON>ttu", "Dark": "Tumma", "Database": "Tietokanta", "December": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Default": "<PERSON><PERSON>", "Default (Open AI)": "<PERSON><PERSON> (Open AI)", "Default (SentenceTransformers)": "<PERSON><PERSON> (SentenceTransformers)", "Default Model": "<PERSON><PERSON><PERSON><PERSON>", "Default model updated": "Oletusmal<PERSON> p<PERSON>tty", "Default Models": "<PERSON><PERSON><PERSON><PERSON>", "Default permissions": "Oletuskäyttöoikeudet", "Default permissions updated successfully": "Oletuskäyttöoikeudet päivitetty onnistuneesti", "Default Prompt Suggestions": "Oletuskehotteiden ehdotukset", "Default to 389 or 636 if TLS is enabled": "Oletus 389 tai 636, jos <PERSON> on käytössä", "Default to ALL": "Oletus KAIKKI", "Default User Role": "Oletuskäyttäj<PERSON><PERSON><PERSON>", "Delete": "Poista", "Delete a model": "Poista malli", "Delete All Chats": "Poista kaikki keskus<PERSON>ut", "Delete All Models": "Poista kaikki mallit", "Delete chat": "Poista keskustelu", "Delete Chat": "Poista keskustelu", "Delete chat?": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän keskustelun?", "Delete folder?": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän kansion?", "Delete function?": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän toim<PERSON>?", "Delete Message": "", "Delete prompt?": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän kehotteen?", "delete this link": "poista täm<PERSON>ki", "Delete tool?": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän työkalun?", "Delete User": "Poista käyttäjä", "Deleted {{deleteModelTag}}": "Poistettu {{deleteModelTag}}", "Deleted {{name}}": "<PERSON><PERSON><PERSON><PERSON> {{nimi}}", "Deleted User": "K<PERSON><PERSON><PERSON>ä<PERSON><PERSON> poistettu", "Describe your knowledge base and objectives": "<PERSON><PERSON><PERSON> tie<PERSON> ja tavo<PERSON>", "Description": "<PERSON><PERSON><PERSON>", "Disabled": "<PERSON><PERSON> k<PERSON>", "Discover a function": "<PERSON><PERSON><PERSON><PERSON> toiminto", "Discover a model": "<PERSON><PERSON><PERSON> malliin", "Discover a prompt": "<PERSON><PERSON><PERSON><PERSON> kehote", "Discover a tool": "Löydä työkalu", "Discover wonders": "Löydä i<PERSON>ä asioita", "Discover, download, and explore custom functions": "<PERSON><PERSON><PERSON>, lataa ja tutki mukautettuja toimint<PERSON>", "Discover, download, and explore custom prompts": "Löydä ja lataa mukautettuja kehotteita", "Discover, download, and explore custom tools": "<PERSON><PERSON><PERSON>, lataa ja tutki mukautettuja työkaluja", "Discover, download, and explore model presets": "Löydä ja lataa mallien es<PERSON>etuksia", "Dismissible": "Ohitettavissa", "Display": "Näytä", "Display Emoji in Call": "Näytä hymiöitä puhelussa", "Display the username instead of You in the Chat": "Näytä käyttäjänimi keskustelussa \"Sinä\" -<PERSON><PERSON><PERSON>an", "Displays citations in the response": "Näyttää lähdeviitteet vastauksessa", "Dive into knowledge": "Uppoudu tietoon", "Do not install functions from sources you do not fully trust.": "<PERSON><PERSON><PERSON> asenna toimint<PERSON> l<PERSON>hteistä, joi<PERSON> et luota t<PERSON>.", "Do not install tools from sources you do not fully trust.": "<PERSON><PERSON><PERSON> asenna ty<PERSON>kalu<PERSON> lähteistä, joi<PERSON> et luota tä<PERSON>.", "Document": "Asiakirja", "Documentation": "Dokumentaatio", "Documents": "Asiakirjat", "does not make any external connections, and your data stays securely on your locally hosted server.": "ei tee ulkoisia yhteyksiä, ja tietosi pysyvät turvallisesti paikallisesti isännöidyllä palvelimellasi.", "Don't have an account?": "<PERSON><PERSON><PERSON> sinulla ole tiliä?", "don't install random functions from sources you don't trust.": "<PERSON><PERSON><PERSON> asenna satunnaisia toimintoja lähteistä, joi<PERSON> et luota.", "don't install random tools from sources you don't trust.": "<PERSON>l<PERSON> asenna satunnaisia työkaluja lähteistä, joi<PERSON> et luota.", "Done": "Val<PERSON>", "Download": "Lataa", "Download canceled": "Lataus peru<PERSON>ttu", "Download Database": "<PERSON><PERSON><PERSON>", "Drag and drop a file to upload or select a file to view": "<PERSON><PERSON><PERSON> ja pudota tiedosto lad<PERSON>i tai valitse tiedosto kats<PERSON>i", "Draw": "<PERSON><PERSON><PERSON>", "Drop any files here to add to the conversation": "Pudota tiedostoja tähän lisätäksesi ne keskusteluun", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "esim. '30s', '10m'. Kel<PERSON>iset aikayksiköt ovat 's', 'm', 'h'.", "e.g. A filter to remove profanity from text": "<PERSON><PERSON><PERSON>, joka poistaa k<PERSON> te<PERSON>", "e.g. My Filter": "es<PERSON>. <PERSON><PERSON>", "e.g. My Tools": "es<PERSON>. <PERSON><PERSON>", "e.g. my_filter": "esim. oma_suodatin", "e.g. my_tools": "esim. omat_työkalut", "e.g. Tools for performing various operations": "esim. työkaluja erilaisten toimenpiteiden suorittamiseen", "Edit": "<PERSON><PERSON><PERSON><PERSON>", "Edit Arena Model": "Muokkaa Arena-mallia", "Edit Channel": "", "Edit Connection": "Muokkaa yhteyttä", "Edit Default Permissions": "Muokkaa oletuskäyttöoikeuksia", "Edit Memory": "Muokkaa muistia", "Edit User": "Muokkaa käyttäjää", "Edit User Group": "Muokkaa käyttäjäryhmää", "ElevenLabs": "ElevenLabs", "Email": "Sähköposti", "Embark on adventures": "Lähde seikkailu<PERSON>", "Embedding Batch Size": "Upotuksen eräkoko", "Embedding Model": "Upotus<PERSON><PERSON>", "Embedding Model Engine": "Upot<PERSON><PERSON><PERSON> moottori", "Embedding model set to \"{{embedding_model}}\"": "\"{{embedding_model}}\" valittu upot<PERSON><PERSON><PERSON><PERSON>", "Enable API Key": "", "Enable autocomplete generation for chat messages": "Ota automaattinen täydennys käyttöön keskusteluviesteissä", "Enable Community Sharing": "<PERSON><PERSON>ön jakaminen käyttöön", "Enable Google Drive": "", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "Ota Memory Locking (mlock) käyttöön estääksesi mallidatan vaihtamisen pois RAM-muistista. Tämä lukitsee mallin työsivut RAM-muistiin, varmistaen että niitä ei vaihdeta levylle. Tämä voi parantaa suorituskykyä välttämällä sivuvikoja ja varmistamalla nopean tietojen käytön.", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "Ota Memory Mapping (mmap) käyttöön ladataksesi mallidataa. Tämä vaihtoehto sallii järjestelmän käyttää levytilaa RAM-laajennuksena käsittelemällä levytiedostoja kuin ne olisivat RAM-muistissa. Tämä voi parantaa mallin suorituskykyä sallimalla nopeamman tietojen käytön. Kuitenkin se ei välttämättä toimi oikein kaikissa järjestelmissä ja voi kuluttaa huomattavasti levytilaa.", "Enable Message Rating": "Ota viestiarviointi käyttöön", "Enable Mirostat sampling for controlling perplexity. (Default: 0, 0 = Disabled, 1 = Mirostat, 2 = Mirostat 2.0)": "Ota Mirostat-näytteenotto käyttöön hallinnan monimerkityksellisyydelle. (Oletus: 0, 0 = <PERSON><PERSON>ä, 1 = Mirostat, 2 = Mirostat 2.0)", "Enable New Sign Ups": "<PERSON><PERSON> uudet rekisteröitymiset", "Enable Web Search": "<PERSON>ta verk<PERSON> k<PERSON>öön", "Enabled": "Käytössä", "Engine": "<PERSON><PERSON><PERSON>", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "Varmista, että CSV-tiedostossasi on 4 saraketta tässä järjestyksessä: <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Salasana, Rooli.", "Enter {{role}} message here": "<PERSON><PERSON><PERSON><PERSON> {{role}}-v<PERSON><PERSON>n", "Enter a detail about yourself for your LLMs to recall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jonka <PERSON>M-ohjelmat voivat muistaa", "Enter api auth string (e.g. username:password)": "Kirjoita API-todennusmerkkijono (esim. käyttäjätunnus:salasana)", "Enter Application DN": "<PERSON><PERSON><PERSON><PERSON> DN", "Enter Application DN Password": "<PERSON><PERSON><PERSON><PERSON> DN-salas<PERSON>", "Enter Bing Search V7 Endpoint": "<PERSON><PERSON><PERSON>ita <PERSON> Search V7 -päätepisteen osoite", "Enter Bing Search V7 Subscription Key": "<PERSON><PERSON><PERSON>ita <PERSON> Search V7 -tilauskäyttäjäavain", "Enter Brave Search API Key": "Kirjoita Brave Search API -avain", "Enter certificate path": "<PERSON><PERSON><PERSON><PERSON>", "Enter CFG Scale (e.g. 7.0)": "<PERSON><PERSON><PERSON><PERSON>-mit<PERSON> (esim. 7.0)", "Enter Chunk Overlap": "Syötä osien päällekkäisyys", "Enter Chunk Size": "Syötä osien koko", "Enter description": "<PERSON><PERSON><PERSON><PERSON>", "Enter Github Raw URL": "<PERSON><PERSON><PERSON><PERSON> -URL-osoite", "Enter Google PSE API Key": "Kirjoita Google PSE API -avain", "Enter Google PSE Engine Id": "Kirjoita Google PSE -moottorin tunnus", "Enter Image Size (e.g. 512x512)": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> k<PERSON> (esim. 512x512)", "Enter Jina API Key": "<PERSON><PERSON><PERSON><PERSON> -avain", "Enter Kagi Search API Key": "", "Enter language codes": "<PERSON><PERSON><PERSON><PERSON>", "Enter Model ID": "<PERSON><PERSON><PERSON><PERSON> mall<PERSON>", "Enter model tag (e.g. {{modelTag}})": "<PERSON><PERSON><PERSON><PERSON> (esim. {{modelTag}})", "Enter Mojeek Search API Key": "<PERSON><PERSON><PERSON><PERSON> Search API -avain", "Enter name or email": "", "Enter Number of Steps (e.g. 50)": "<PERSON><PERSON><PERSON><PERSON> m<PERSON> (esim. 50)", "Enter proxy URL (e.g. **************************:port)": "Kirjoita välityspalvelimen URL-osoite (esim. https://käyttäjä:salasana@host:port<PERSON>)", "Enter Sampler (e.g. Euler a)": "<PERSON><PERSON><PERSON><PERSON> (esim. Euler a)", "Enter Scheduler (e.g. Karras)": "<PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)", "Enter Score": "<PERSON><PERSON><PERSON><PERSON>", "Enter SearchApi API Key": "Kirjoita SearchApi API -avain", "Enter SearchApi Engine": "<PERSON><PERSON><PERSON><PERSON>-moot<PERSON><PERSON>", "Enter Searxng Query URL": "<PERSON><PERSON><PERSON><PERSON>-k<PERSON><PERSON>n URL-osoite", "Enter Seed": "<PERSON><PERSON><PERSON><PERSON>", "Enter Serper API Key": "<PERSON><PERSON><PERSON><PERSON> -avain", "Enter Serply API Key": "<PERSON><PERSON><PERSON><PERSON> Serply API -avain", "Enter Serpstack API Key": "<PERSON><PERSON><PERSON><PERSON> API -avain", "Enter server host": "<PERSON><PERSON><PERSON><PERSON> p<PERSON> isä<PERSON><PERSON><PERSON><PERSON>", "Enter server label": "<PERSON><PERSON><PERSON><PERSON> palvelimen tunniste", "Enter server port": "<PERSON><PERSON><PERSON><PERSON> palvel<PERSON> portti", "Enter stop sequence": "<PERSON><PERSON><PERSON><PERSON>", "Enter system prompt": "<PERSON><PERSON><PERSON><PERSON>stelmäkehote", "Enter Tavily API Key": "<PERSON><PERSON><PERSON><PERSON> -avain", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter Tika Server URL": "<PERSON><PERSON><PERSON><PERSON> URL", "Enter Top K": "<PERSON><PERSON><PERSON><PERSON>", "Enter URL (e.g. http://127.0.0.1:7860/)": "<PERSON><PERSON><PERSON><PERSON> (esim. http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "<PERSON><PERSON><PERSON><PERSON> (esim. http://localhost:11434)", "Enter your current password": "", "Enter Your Email": "<PERSON><PERSON><PERSON><PERSON>köpostiosoitteesi", "Enter Your Full Name": "<PERSON><PERSON><PERSON><PERSON> koko ni<PERSON>i", "Enter your message": "<PERSON><PERSON><PERSON><PERSON>", "Enter your new password": "", "Enter Your Password": "<PERSON><PERSON><PERSON><PERSON>", "Enter your prompt": "", "Enter Your Role": "<PERSON><PERSON><PERSON><PERSON>", "Enter Your Username": "<PERSON><PERSON><PERSON><PERSON>", "Enter your webhook URL": "", "Error": "<PERSON><PERSON><PERSON>", "ERROR": "VIRHE", "Error accessing Google Drive: {{error}}": "", "Error uploading file: {{error}}": "", "Evaluations": "Arvioinnit", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "Esimerkki: (&(objectClass=inetOrgPerson)(uid=%s))", "Example: ALL": "Esimerkki: KAIKKI", "Example: ou=users,dc=foo,dc=example": "Esimerkki: ou=käyttäjät,dc=foo,dc=example", "Example: sAMAccountName or uid or userPrincipalName": "Esimerkki: sAMAccountName tai uid tai userPrincipalName", "Exclude": "<PERSON><PERSON><PERSON> pois", "Experimental": "<PERSON><PERSON><PERSON><PERSON>", "Explore the cosmos": "<PERSON><PERSON><PERSON>", "Export": "Vie", "Export All Archived Chats": "Vie kaikki arkistoidut keskustelut", "Export All Chats (All Users)": "Vie kaikki keskustelut (kaikki käyttäjät)", "Export chat (.json)": "<PERSON><PERSON> kes<PERSON> (.json)", "Export Chats": "Vie keskustelut", "Export Config to JSON File": "<PERSON><PERSON>-tied<PERSON><PERSON>", "Export Functions": "<PERSON><PERSON>", "Export Models": "<PERSON><PERSON> malleja", "Export Presets": "<PERSON><PERSON>", "Export Prompts": "<PERSON>ie keh<PERSON>et", "Export to CSV": "Vie CSV-<PERSON><PERSON><PERSON>", "Export Tools": "Vie t<PERSON>ökalut", "External Models": "Ulk<PERSON>t mallit", "Extremely bad": "", "Failed to add file.": "Tiedoston lisääminen epäonnistui.", "Failed to create API Key.": "API-avaimen luonti epäonnistui.", "Failed to read clipboard contents": "Leikepöydän sisä<PERSON>ön lukeminen epäonnistui", "Failed to save models configuration": "<PERSON><PERSON> m<PERSON>ks<PERSON> tallentaminen epäonnistui", "Failed to update settings": "Asetusten päivittäminen epäonnistui", "February": "<PERSON><PERSON><PERSON><PERSON>", "Feedback History": "Palautehistoria", "Feedbacks": "Palautteet", "File": "Tiedosto", "File added successfully.": "Tiedosto lisätty onnistuneesti.", "File content updated successfully.": "Tiedoston sisältö päivitetty onnistuneesti.", "File Mode": "Tiedostotila", "File not found.": "Tiedostoa ei lö<PERSON>.", "File removed successfully.": "Tiedosto poistettu onnistuneesti.", "File size should not exceed {{maxSize}} MB.": "Tiedoston koko ei saa ylittää {{maxSize}} MB.", "File uploaded successfully": "", "Files": "<PERSON><PERSON><PERSON><PERSON>", "Filter is now globally disabled": "<PERSON><PERSON><PERSON> on nyt poistettu käytöstä globaalisti", "Filter is now globally enabled": "<PERSON><PERSON><PERSON> on nyt otettu k<PERSON>yttöön globaalisti", "Filters": "<PERSON><PERSON><PERSON><PERSON>", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Sormenjäljen väärentäminen havaittu: Alkukirjaimia ei voi käyttää avatarina. Käytetään oletusprofiilikuvaa.", "Fluidly stream large external response chunks": "Virtaa suuria ulkoisia vast<PERSON><PERSON> j<PERSON>", "Focus chat input": "Fokusoi syöttökenttään", "Folder deleted successfully": "<PERSON><PERSON><PERSON> pois<PERSON> onnist<PERSON>", "Folder name cannot be empty": "Kansion nimi ei voi olla tyhjä", "Folder name cannot be empty.": "Kansion nimi ei voi olla tyhjä.", "Folder name updated successfully": "Ka<PERSON><PERSON> nimi p<PERSON>iv<PERSON>tty onnistuneesti", "Forge new paths": "Luo uusia polkuja", "Form": "Lomake", "Format your variables using brackets like this:": "Muotoile muuttujasi hakasulkeilla tällä tavalla:", "Frequency Penalty": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Function": "<PERSON><PERSON><PERSON><PERSON>", "Function created successfully": "<PERSON><PERSON><PERSON><PERSON> luotu onnist<PERSON><PERSON>i", "Function deleted successfully": "<PERSON><PERSON><PERSON><PERSON> pois<PERSON>u onnistuneesti", "Function Description": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "Function ID": "<PERSON><PERSON><PERSON><PERSON> tunnus", "Function is now globally disabled": "<PERSON><PERSON><PERSON><PERSON> on nyt poistettu käytöstä globaalisti", "Function is now globally enabled": "<PERSON><PERSON><PERSON><PERSON> on nyt otettu käyttöön globaalisti", "Function Name": "<PERSON><PERSON><PERSON><PERSON> nimi", "Function updated successfully": "<PERSON><PERSON><PERSON><PERSON> p<PERSON> onnistuneesti", "Functions": "<PERSON><PERSON><PERSON><PERSON>", "Functions allow arbitrary code execution": "<PERSON><PERSON><PERSON><PERSON> sallivat mi<PERSON><PERSON> koodin su<PERSON>n", "Functions allow arbitrary code execution.": "<PERSON><PERSON><PERSON><PERSON> sallivat mi<PERSON> koodin su<PERSON>n.", "Functions imported successfully": "<PERSON><PERSON><PERSON><PERSON> tuotu onnist<PERSON><PERSON>i", "General": "<PERSON><PERSON><PERSON>", "General Settings": "<PERSON><PERSON><PERSON><PERSON>", "Generate Image": "<PERSON><PERSON> kuva", "Generating search query": "<PERSON><PERSON><PERSON>", "Get started": "Aloita", "Get started with {{WEBUI_NAME}}": "<PERSON><PERSON><PERSON> {{WEBUI_NAME}}:iä", "Global": "<PERSON><PERSON><PERSON>", "Good Response": "Hyvä <PERSON>aus", "Google Drive": "", "Google PSE API Key": "Google PSE API -avain", "Google PSE Engine Id": "Google PSE -moottorin tunnus", "Group created successfully": "<PERSON><PERSON><PERSON><PERSON> luotu onnistuneesti", "Group deleted successfully": "<PERSON><PERSON><PERSON><PERSON> poistettu onnistuneesti", "Group Description": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "Group Name": "<PERSON><PERSON><PERSON><PERSON><PERSON> nimi", "Group updated successfully": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>iv<PERSON><PERSON> onnistuneesti", "Groups": "<PERSON><PERSON><PERSON><PERSON>", "GSA Chat can make mistakes. Review all responses for accuracy.": "", "h:mm a": "h:mm a", "Haptic Feedback": "<PERSON><PERSON><PERSON> palaute", "Harmful or offensive": "", "has no conversations.": "ei ole keskusteluja.", "Hello, {{name}}": "Hei, {{name}}", "Help": "<PERSON><PERSON>", "Help us create the best community leaderboard by sharing your feedback history!": "Auta meitä luomaan paras yhtei<PERSON>ön t<PERSON>lo jaka<PERSON> palautehistoriasi!", "Hex Color": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Hex Color - Leave empty for default color": "He<PERSON><PERSON>imaaliväri - Jät<PERSON> t<PERSON>j<PERSON><PERSON>, jos haluat o<PERSON>", "Hide": "<PERSON><PERSON><PERSON>", "Host": "Palvelin", "How can I help you today?": "Miten voin auttaa sinua tänään?", "How would you rate this response?": "<PERSON>inka arvioisit tätä vastausta?", "Hybrid Search": "<PERSON><PERSON><PERSON><PERSON>", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "<PERSON><PERSON><PERSON><PERSON>, että olen lukenut ja ymmärrän toimintani se<PERSON>. <PERSON>n tietoinen mieli<PERSON>sen koodin suorittamiseen liittyvistä riskeistä ja olen varmistanut lähteen luotettavuuden.", "ID": "<PERSON><PERSON><PERSON>", "Ignite curiosity": "Sytytä uteliaisuus", "Image Compression": "", "Image Generation (Experimental)": "<PERSON><PERSON><PERSON><PERSON><PERSON> (kokeellinen)", "Image Generation Engine": "Kuvagener<PERSON><PERSON>ott<PERSON>", "Image Max Compression Size": "", "Image Settings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Images": "<PERSON><PERSON>", "Import Chats": "<PERSON><PERSON> kes<PERSON>", "Import Config from JSON File": "<PERSON><PERSON>SON-tiedostosta", "Import Functions": "<PERSON><PERSON>", "Import Models": "<PERSON><PERSON> malleja", "Import Presets": "<PERSON><PERSON>", "Import Prompts": "<PERSON><PERSON> keh<PERSON>et", "Import Tools": "<PERSON><PERSON>", "Include": "Sisällytä", "Include `--api-auth` flag when running stable-diffusion-webui": "Sisällytä `--api-auth`-lippu ajetta<PERSON>a stable-diffusion-webui", "Include `--api` flag when running stable-diffusion-webui": "Sisällytä `--api`-lippu ajettaessa stable-diffusion-webui", "Incomplete response": "", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive. (Default: 0.1)": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kuinka nopeasti algoritmi reagoi tuotetusta tekstistä saatuun palautteeseen. Alhaisempi oppimisaste johtaa hitaampiin säätöihin, kun taas korkeampi oppimisaste tekee algoritmista reaktiivisemman. (Oletus: 0.1)", "Info": "<PERSON><PERSON><PERSON>", "Input commands": "Syötekäskyt", "Install from Github URL": "<PERSON><PERSON><PERSON>-URL:stä", "Instant Auto-Send After Voice Transcription": "Heti automaattinen lähetys äänitunnistuksen jälkeen", "Interface": "Käyttöliittymä", "Invalid file format.": "<PERSON><PERSON><PERSON><PERSON><PERSON> tied<PERSON>.", "Invalid Tag": "<PERSON><PERSON><PERSON><PERSON><PERSON> tagi", "is typing...": "", "January": "tammikuu", "Jina API Key": "Jina API -avain", "join our Discord for help.": "liity <PERSON>rdiimme sa<PERSON> a<PERSON>a.", "JSON": "JSON", "JSON Preview": "JSON-esikatselu", "July": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "June": "kesä<PERSON>u", "JWT Expiration": "JWT-vanheneminen", "JWT Token": "JWT-token", "Kagi Search API Key": "", "Keep Alive": "Pysy aktiivisena", "Key": "Avain", "Keyboard shortcuts": "Pikanäppäimet", "Knowledge": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Knowledge Access": "<PERSON><PERSON><PERSON>", "Knowledge created successfully.": "Tietokanta luotu onnistuneesti.", "Knowledge deleted successfully.": "Tietokanta poistettu onnistuneesti.", "Knowledge reset successfully.": "Tietokanta nollattu onnistuneesti.", "Knowledge updated successfully": "Tietokanta päivitetty onnistuneesti", "Label": "<PERSON><PERSON><PERSON><PERSON>", "Landing Page Mode": "<PERSON><PERSON><PERSON><PERSON> tila", "Language": "<PERSON><PERSON>", "Last Active": "Viimeksi aktiivinen", "Last Modified": "<PERSON>iimeks<PERSON> muo<PERSON>", "Last reply": "", "Latest users": "", "LDAP": "LDAP", "LDAP server updated": "LDAP-pal<PERSON><PERSON> p<PERSON>", "Leaderboard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Leave empty for unlimited": "Jätä tyhjäksi rajattomaksi", "Leave empty to include all models from \"{{URL}}/api/tags\" endpoint": "<PERSON><PERSON><PERSON> t<PERSON>, jos haluat sisällyttää kaikki mallit \"{{URL}}/api/tags\" -päätepistestä", "Leave empty to include all models from \"{{URL}}/models\" endpoint": "<PERSON><PERSON><PERSON> t<PERSON>, jos haluat sisällyttää kaikki mallit \"{{URL}}/models\" -päätepistestä", "Leave empty to include all models or select specific models": "<PERSON><PERSON><PERSON> t<PERSON><PERSON><PERSON>, jos haluat sisällyttää kaikki mallit tai valitse tietyt mallit", "Leave empty to use the default prompt, or enter a custom prompt": "Jätä tyhjäksi käyttääksesi oletuskehotetta tai kirjoita mukautettu kehote", "Light": "Vaalea", "Listening...": "<PERSON><PERSON><PERSON><PERSON>...", "Local": "<PERSON><PERSON><PERSON><PERSON>", "Local Models": "Paikalliset mallit", "Lost": "Men<PERSON><PERSON>", "LTR": "LTR", "Made by OpenWebUI Community": "Tehnyt OpenWebUI-yhteisö", "Make sure to enclose them with": "Varmista, että suljet ne", "Make sure to export a workflow.json file as API format from ComfyUI.": "Muista viedä workflow.json-tiedosto API-muodossa ComfyUI:sta.", "Manage": "Hallitse", "Manage Arena Models": "Hallitse Arena-malleja", "Manage Ollama": "<PERSON><PERSON><PERSON>", "Manage Ollama API Connections": "Hallitse Ollama API -yhteyksiä", "Manage OpenAI API Connections": "Hallitse OpenAI API -yhteyksiä", "Manage Pipelines": "<PERSON><PERSON><PERSON>", "March": "ma<PERSON><PERSON><PERSON>", "Max Tokens (num_predict)": "Tokenien enimmäismäärä (num_predict)", "Max Upload Count": "Latausten enimmäismäärä", "Max Upload Size": "Latausten enimmäiskoko", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Enintään 3 mallia voidaan ladata samanaikaisesti. Yritä myöhemmin uudelleen.", "May": "<PERSON><PERSON><PERSON><PERSON>", "Memories accessible by LLMs will be shown here.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, j<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON> käyttävät, näkyvät tässä.", "Memory": "<PERSON><PERSON><PERSON>", "Memory added successfully": "<PERSON><PERSON><PERSON> l<PERSON>nistuneesti", "Memory cleared successfully": "<PERSON><PERSON><PERSON> onnistuneesti", "Memory deleted successfully": "<PERSON><PERSON><PERSON> pois<PERSON>ttu onnist<PERSON>i", "Memory updated successfully": "<PERSON><PERSON><PERSON> p<PERSON>iv<PERSON>tty onnistuneesti", "Merge Responses": "Yhdistä vastaukset", "Message rating should be enabled to use this feature": "Tämän toiminnon käyttämiseksi viestiarviointi on otettava käyttöön", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "Linkin luomisen jälkeen lähettämäsi viestit eivät ole jaettuja. Käyttäjät, j<PERSON>la on URL-osoite, voivat tarkastella jaettua keskustelua.", "Min P": "<PERSON>", "Minimum Score": "Vähimmäispisteet", "Mirostat": "Mirostat", "Mirostat Eta": "Mirostat Eta", "Mirostat Tau": "Mirostat Tau", "MMMM DD, YYYY": "D. MMMM YYYY", "MMMM DD, YYYY HH:mm": "D. <PERSON>M YYYY, HH:mm", "MMMM DD, YYYY hh:mm:ss A": "<PERSON>. <PERSON>M YYYY, hh:mm:ss a", "Model": "<PERSON><PERSON>", "Model '{{modelName}}' has been successfully downloaded.": "<PERSON>i '{{modelName}}' ladattiin onnist<PERSON>esti.", "Model '{{modelTag}}' is already in queue for downloading.": "<PERSON><PERSON> '{{modelTag}}' on jo jono<PERSON>.", "Model {{modelId}} not found": "<PERSON><PERSON> {{modelId}} ei l<PERSON>yt", "Model {{modelName}} is not vision capable": "Malli {{modelName}} ei kykene n<PERSON>yn", "Model {{name}} is now {{status}}": "Malli {{name}} on nyt {{status}}", "Model accepts image inputs": "Malli hyväksyy kuvasyötteitä", "Model created successfully!": "<PERSON>i luotu onnistuneesti!", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "<PERSON><PERSON>j<PERSON>rjestelmäpolku havaittu. <PERSON><PERSON> lyhytnimi vaadit<PERSON> p<PERSON>, ei voida jatkaa.", "Model Filtering": "<PERSON><PERSON> suodatus", "Model ID": "<PERSON><PERSON> tunnus", "Model IDs": "Mallitunnukset", "Model Name": "<PERSON><PERSON> nimi", "Model not selected": "Mallia ei ole valittu", "Model Params": "<PERSON>in parametrit", "Model Permissions": "<PERSON><PERSON>", "Model updated successfully": "<PERSON><PERSON> p<PERSON> onnistuneesti", "Modelfile Content": "Mallitiedoston sisältö", "Models": "Mallit", "Models Access": "<PERSON><PERSON>", "Models configuration saved successfully": "<PERSON><PERSON> m<PERSON>ritykset tallennettu onnistuneesti", "Mojeek Search API Key": "Mojeek Search API -avain", "more": "lisää", "More": "Lisää", "Name": "<PERSON><PERSON>", "Name your knowledge base": "<PERSON>", "New Chat": "Uusi keskustelu", "New folder": "", "New Password": "<PERSON><PERSON><PERSON>", "new-channel": "", "No content found": "Sisältöä ei l<PERSON>yt", "No content to speak": "Ei puhuttavaa sisältöä", "No distance available": "Etäisyyttä ei saatavilla", "No feedbacks found": "Palautteita ei lö<PERSON>", "No file selected": "Tiedostoa ei ole valittu", "No files found.": "Tiedostoja ei löytynyt.", "No groups with access, add a group to grant access": "<PERSON><PERSON> ryhm<PERSON>, j<PERSON><PERSON> on pääsy, lis<PERSON><PERSON> ryhmä antaaksesi pääsyn", "No HTML, CSS, or JavaScript content found.": "HTML-, CSS- tai <PERSON>Script-sisältöä ei löytynyt.", "No knowledge found": "Tietoa ei l<PERSON>", "No model IDs": "<PERSON><PERSON> mall<PERSON>", "No models found": "Malleja ei lö<PERSON>", "No models selected": "<PERSON>eja ei ole valittu", "No results found": "<PERSON><PERSON> tuloksia", "No search query generated": "Hakukyselyä ei luotu", "No source available": "Lähdettä ei saatavilla", "No users were found.": "Käyttäjiä ei lö<PERSON>.", "No valves to update": "Ei venttiileitä päivitettäväksi", "None": "<PERSON><PERSON> mi<PERSON>n", "Not accurate": "", "Not relevant": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Huomautus: <PERSON><PERSON> v<PERSON>äispistemä<PERSON><PERSON><PERSON><PERSON>, haku palauttaa vain sellaiset asiakirjat, joiden pistemäärä on vähintään vähimmäismäärä.", "Notes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Notification Sound": "", "Notification Webhook": "", "Notifications": "Ilmoitukset", "November": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "num_gpu (Ollama)": "num_gpu (Ollama)", "num_thread (Ollama)": "num_thread (Ollama)", "OAuth ID": "OAuth-tunnus", "October": "lokakuu", "Off": "<PERSON><PERSON> p<PERSON>", "Okay, Let's Go!": "<PERSON><PERSON>, menn<PERSON><PERSON>n!", "OLED Dark": "OLED-tumma", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API disabled": "Ollama API poistettu käytöstä", "Ollama API settings updated": "Ollama API -asetukset päivitetty", "Ollama Version": "Ollama-versio", "On": "Päällä", "Only alphanumeric characters and hyphens are allowed": "<PERSON><PERSON> k<PERSON>, numerot ja väliviivat ovat sallittuja", "Only alphanumeric characters and hyphens are allowed in the command string.": "<PERSON><PERSON>, numerot ja väliviivat ovat sallittuja komentosarjassa.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "<PERSON>ain koko<PERSON>mia voi muokata, luo uusi tietokanta muokataksesi/lisätäks<PERSON> as<PERSON>kir<PERSON>.", "Only select users and groups with permission can access": "Vain valitut käyttäjät ja ryhm<PERSON>, j<PERSON><PERSON> on käyttöoikeus, pääsevät käyttämään", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Hups! Näyttää siltä, että URL-osoite on virheellinen. Tarkista se ja yritä uudelleen.", "Oops! There are files still uploading. Please wait for the upload to complete.": "Hups! Tiedost<PERSON> on vielä ladattavana. Odota, ett<PERSON> lataus on valmis.", "Oops! There was an error in the previous response.": "Hups! Edellisessä vastauksessa oli virhe.", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Hups! Käytät ei-tuettua menetelm<PERSON>ä (vain frontend). Palvele WebUI:ta backendistä.", "Open in full screen": "<PERSON><PERSON> koko n<PERSON>n tilaan", "Open new chat": "Avaa uusi keskustelu", "Open WebUI uses faster-whisper internally.": "Open WebUI käyttää faster-<PERSON><PERSON> si<PERSON>.", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "Open WebUI käyttää SpeechT5:tä ja CMU Arctic -kaiuttimen upotuksia.", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "Open WebUI -versio (v{{OPEN_WEBUI_VERSION}}) on alempi kuin vaadittu versio (v{{REQUIRED_VERSION}})", "OpenAI": "OpenAI", "OpenAI API": "OpenAI API", "OpenAI API Config": "OpenAI API -asetukset", "OpenAI API Key is required.": "OpenAI API -avain vaaditaan.", "OpenAI API settings updated": "OpenAI API -asetukset päivitetty", "OpenAI URL/Key required.": "OpenAI URL/avain vaaditaan.", "or": "tai", "Organize your users": "Järjestä k<PERSON>täjäsi", "OUTPUT": "TULOSTE", "Output format": "<PERSON><PERSON><PERSON> muoto", "Overview": "Yleiskatsaus", "page": "sivu", "Password": "<PERSON><PERSON><PERSON>", "Paste Large Text as File": "Liitä suuri teksti tied<PERSON>", "PDF document (.pdf)": "PDF-as<PERSON><PERSON><PERSON><PERSON> (.pdf)", "PDF Extract Images (OCR)": "Poimi kuvat PDF:stä (OCR)", "pending": "odottaa", "Permission denied when accessing media devices": "Käyttöoikeus epäitty media-laitteille", "Permission denied when accessing microphone": "Käyttöoikeus epäitty mikrofonille", "Permission denied when accessing microphone: {{error}}": "Käyttöoikeus epäitty mikrofonille: {{error}}", "Permissions": "Käyttöoikeudet", "Personalization": "<PERSON><PERSON><PERSON>", "Pin": "Kiinnitä", "Pinned": "<PERSON><PERSON><PERSON><PERSON>", "Pioneer insights": "<PERSON><PERSON>", "Pipeline deleted successfully": "<PERSON><PERSON> poistettu onnist<PERSON>esti", "Pipeline downloaded successfully": "<PERSON><PERSON> ladattu onnist<PERSON>esti", "Pipelines": "Putkistot", "Pipelines Not Detected": "Putkistoja ei havaittu", "Pipelines Valves": "Put<PERSON><PERSON><PERSON><PERSON>", "Plain text (.txt)": "Pelkk<PERSON> teksti (.txt)", "Playground": "Leikkipaikka", "Please carefully review the following warnings:": "Tarkista huolellisesti se<PERSON> var<PERSON>:", "Please enter a prompt": "<PERSON><PERSON><PERSON><PERSON> kehote", "Please fill in all fields.": "Täytä kaikki kentät.", "Please select a model first.": "Valitse ensin malli.", "Port": "<PERSON><PERSON>", "Prefix ID": "Etuliite-ID", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "Etuliite-ID:tä käytetään välttämään ristiriidat muiden yhteyksien kanssa lisäämällä etuliite mallitunnuksiin - jätä tyhjäksi, jos haluat ottaa sen pois käytöstä", "Previous 30 days": "Edelliset 30 päivää", "Previous 7 days": "Edelliset 7 päivää", "Profile Image": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "<PERSON><PERSON><PERSON> (es<PERSON>. <PERSON><PERSON> fakta <PERSON> valtakunnasta)", "Prompt Content": "Kehotteen sisältö", "Prompt created successfully": "<PERSON><PERSON><PERSON> luotu on<PERSON>", "Prompt suggestions": "Kehotteen ehdotukset", "Prompt updated successfully": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> onnistuneesti", "Prompts": "Ke<PERSON><PERSON><PERSON>", "Prompts Access": "Kehoitteiden käyttöoikeudet", "Provide any specific details": "", "Proxy URL": "Välityspalvelimen URL-osoite", "Pull \"{{searchValue}}\" from Ollama.com": "Lataa \"{{searchValue}}\" Ollama.comista", "Pull a model from Ollama.com": "Lataa malli Ollama.comista", "Query Generation Prompt": "Kyselytulosten luontikehote", "Query Params": "Kyselyparametrit", "RAG Template": "RAG-malline", "Rating": "Arviointi", "Re-rank models by topic similarity": "Uudelleenjärjestä mallit aiheyhteyden mukaan", "Read Aloud": "<PERSON><PERSON>", "Record voice": "<PERSON><PERSON><PERSON><PERSON>", "Redirecting you to OpenWebUI Community": "Ohjataan sinut OpenWebUI-yhteisöön", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative. (Default: 40)": "Vähentää merkityksetöntä sisältöä tuottavan todennäköisyyttä. Korkeampi arvo (esim. 100) antaa monipuolisempia vastauksia, kun taas alhaisempi arvo (esim. 10) on konservatiivisempi. (<PERSON><PERSON>: 40)", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "Viittaa itseen \"Käyttäjänä\" (esim. \"Käyttäjä opiskelee espanjaa\")", "References from": "Viitteet lähteistä", "Refresh Token Expiration": "", "Regenerate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Release Notes": "Julkaisutiedot", "Relevance": "<PERSON><PERSON><PERSON><PERSON>", "Remove": "Poista", "Remove Model": "Poista malli", "Rename": "<PERSON><PERSON><PERSON>", "Reorder Models": "Uudelleenjärjest<PERSON> malleja", "Repeat Last N": "Toista viimeiset N", "Reply in Thread": "", "Request Mode": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Reranking Model": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Reranking model disabled": "Uudelleenpisteytymismalli poistettu käytöstä", "Reranking model set to \"{{reranking_model}}\"": "\"{{reranking_model}}\" valittu uudelleenpisteytysmalliksi", "Reset": "<PERSON><PERSON><PERSON>", "Reset All Models": "<PERSON><PERSON><PERSON> kaikki mallit", "Reset Upload Directory": "<PERSON><PERSON><PERSON>", "Reset Vector Storage/Knowledge": "Tyhjennä vektoritallennukset/tietämys", "Reset view": "", "Response generation stopped": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "Vastausilmoituksia ei voida otta<PERSON> k<PERSON>öö<PERSON>, koska verkkosivuston käyttöoikeudet on evätty. Myönnä tarvittavat käyttöoikeudet selaimesi asetuksista.", "Response splitting": "<PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON>n", "Result": "<PERSON><PERSON>", "Retrieval Query Generation": "<PERSON><PERSON><PERSON><PERSON><PERSON> luo<PERSON>n", "Rich Text Input for Chat": "Rikasteksti-<PERSON><PERSON><PERSON><PERSON> chattiin", "RK": "RK", "Role": "<PERSON><PERSON><PERSON>", "Rosé Pine": "Ros<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "<PERSON><PERSON><PERSON>", "Running": "K<PERSON>ynnissä", "Save": "<PERSON><PERSON><PERSON>", "Save & Create": "<PERSON><PERSON>na ja luo", "Save & Update": "Tall<PERSON>na ja päivitä", "Save As Copy": "<PERSON><PERSON><PERSON> k<PERSON>a", "Save Tag": "<PERSON><PERSON><PERSON> tagi", "Saved": "Tallennettu", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Keskustelulokien tallentaminen suoraan selaimen tallennustilaan ei ole enää tuettu. Lataa ja poista keskustelulokit napsauttamalla alla olevaa painiketta. <PERSON><PERSON><PERSON>, voit helposti tuoda keskustelulokit takaisin backendiin", "Scroll to bottom when switching between branches": "Vierittää alaspäin vaihdettaessa haarojen välillä", "Search": "<PERSON><PERSON>", "Search a model": "Hae mallia", "Search Base": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Search Chats": "Hae keskusteluja", "Search Collection": "<PERSON><PERSON> koko<PERSON>a", "Search Filters": "Hakusuodattimet", "search for tags": "hae tageja", "Search Functions": "<PERSON><PERSON> to<PERSON>", "Search Knowledge": "<PERSON>e tietämystä", "Search Models": "<PERSON><PERSON> malleja", "Search options": "Hakuvaihtoehdot", "Search Prompts": "Hae kehotteia", "Search Result Count": "Hakutulosten määrä", "Search the web": "Etsi verkosta", "Search Tools": "Hae työkaluja", "Search users": "", "SearchApi API Key": "SearchApi API -avain", "SearchApi Engine": "SearchApi-moottori", "Searched {{count}} sites": "", "Searching \"{{searchQuery}}\"": "<PERSON><PERSON><PERSON> \"{{searchQuery}}\"", "Searching Knowledge for \"{{searchQuery}}\"": "<PERSON><PERSON><PERSON> tiet<PERSON> \"{{searchQuery}}\"", "Searxng Query URL": "Searxng-k<PERSON><PERSON>n <PERSON>-osoite", "See readme.md for instructions": "<PERSON><PERSON> readme.md-tiedost<PERSON><PERSON>", "See what's new": "<PERSON><PERSON>, mit<PERSON> uutta", "Seed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Select a base model": "Valitse <PERSON>li", "Select a engine": "<PERSON><PERSON><PERSON> moottori", "Select a function": "Valitse toiminto", "Select a group": "Valitse ryhmä", "Select a model": "Valitse malli", "Select a pipeline": "<PERSON><PERSON><PERSON> <PERSON>ki", "Select a pipeline url": "Valitse putken URL-osoite", "Select a tool": "Valitse työkalu", "Select Engine": "<PERSON><PERSON><PERSON> moottori", "Select Knowledge": "Valitse tietämys", "Select model": "Valitse malli", "Select only one model to call": "Valitse vain yksi malli kutsuttavaksi", "Selected model(s) do not support image inputs": "Valitut mallit eivät tue kuvasöytteitä", "Semantic distance to query": "Semanttinen etä<PERSON><PERSON><PERSON>", "Send": "Lähetä", "Send a message": "", "Send a Message": "Lähetä viesti", "Send message": "Lähetä viesti", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "Lähettää `stream_options: { include_usage: true }` pyynnössä.\nTuetut tarjoajat palauttavat tokenkäyttötiedot vastauksessa, kun se on asetettu.", "September": "syyskuu", "Serper API Key": "Serper API -avain", "Serply API Key": "Serply API -avain", "Serpstack API Key": "Serpstack API -avain", "Server connection verified": "Palvelinyht<PERSON><PERSON> v<PERSON>", "Set as default": "Aseta oletukseksi", "Set CFG Scale": "Aseta CFG-mitta", "Set Default Model": "<PERSON><PERSON>", "Set embedding model": "<PERSON><PERSON>", "Set embedding model (e.g. {{model}})": "<PERSON><PERSON> upotelmamalli (esim. {{model}})", "Set Image Size": "Aseta kuvan koko", "Set reranking model (e.g. {{model}})": "<PERSON><PERSON> u<PERSON>lleenpisteytymismalli (esim. {{model}})", "Set Sampler": "Aseta näytteistäjä", "Set Scheduler": "<PERSON><PERSON> a<PERSON>n", "Set Steps": "Aseta askeleet", "Set Task Model": "Aseta tehtävämalli", "Set the number of GPU devices used for computation. This option controls how many GPU devices (if available) are used to process incoming requests. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "Aseta käytettyjen GPU-laitteiden määrä laskentaa varten. Tämä asetus kontrolloi, kuinka monta GPU-laitetta (jos saatavilla) käytetään saapuvien pyyntöjen käsittelyyn. <PERSON><PERSON><PERSON> ka<PERSON> voi parantaa suorituskykyä merkittävästi malleissa, jotka on optimoitu GPU-kiihdytykseen, mutta voi myös kuluttaa enemmän virtaa ja GPU-resursseja.", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "Aseta työntekijäsäikeiden määrä laskentaa varten. Tämä asetus kontrolloi, kuinka monta säiettä käytetään saapuvien pyyntöjen rinnakkaiseen käsittelyyn. <PERSON><PERSON><PERSON> kasvat<PERSON>n voi parantaa suorituskykyä suurissa samanaikaisissa työkuormissa, mutta voi myös kuluttaa enemmän keskussuorittimen resursseja.", "Set Voice": "<PERSON><PERSON>", "Set whisper model": "Aseta whisper-malli", "Sets how far back for the model to look back to prevent repetition. (Default: 64, 0 = disabled, -1 = num_ctx)": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kuinka kauas taaksep<PERSON>in malli katsoo välttääkseen to<PERSON>oa. (Oletus: 64, 0 = pois käytöstä, -1 = num_ctx)", "Sets how strongly to penalize repetitions. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. (Default: 1.1)": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kuinka voimakkaasti toistoihin määrät<PERSON><PERSON><PERSON> sankti<PERSON>. Korkeampi arvo (esim. 1,5) rank<PERSON><PERSON> to<PERSON><PERSON> v<PERSON>, kun taas alhaisempi arvo (esim. 0,9) on lempeämpi. (Oletus: 1,1)", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt. (Default: random)": "Määrittää satunnaislukujen siemenen käytettäväksi generoinnissa. Tämän as<PERSON><PERSON>n tiettyyn numeroon saa mallin tuottamaan saman tekstin samalle kehoteelle. (Oletus: satunnainen)", "Sets the size of the context window used to generate the next token. (Default: 2048)": "Määrittää kontekstiikkunan koon, jota käytetään seuraavan tokenin tuotta<PERSON>en. (Oletus: 2048)", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "Määrittää käytettävät lopetussekvenssit. Kun tämä kuvio <PERSON>, LLM lopettaa tekstin tuottamisen ja palauttaa. Useita lopetuskuvioita voidaan asettaa määrittämällä useita erillisiä lopetusparametreja mallitiedostoon.", "Settings": "Asetukset", "Settings saved successfully!": "Asetukset tallennettu onnistuneesti!", "Share": "Jaa", "Share Chat": "Jaa keskustelu", "Share to OpenWebUI Community": "Jaa OpenWebUI-yhteisöön", "Show": "Näytä", "Show \"What's New\" modal on login": "Näytä \"Mitä uutta\" -modaali kirjautumisen yhteydessä", "Show Admin Details in Account Pending Overlay": "Näytä ylläpitäjän tiedot odottavan tilin päällä", "Show shortcuts": "Näytä pikanäppäimet", "Show your support!": "<PERSON><PERSON><PERSON> tukesi!", "Sign in": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Sign in to {{WEBUI_NAME}}": "<PERSON><PERSON><PERSON><PERSON><PERSON> si<PERSON>n palveluun {{WEBUI_NAME}}", "Sign in to {{WEBUI_NAME}} with LDAP": "<PERSON><PERSON><PERSON><PERSON><PERSON> si<PERSON>än palveluun {{WEBUI_NAME}} LDAP:lla", "Sign Out": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Sign up": "Rekisteröidy", "Sign up to {{WEBUI_NAME}}": "Rekisteröidy palveluun {{WEBUI_NAME}}", "Signing in to {{WEBUI_NAME}}": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> si<PERSON>än palveluun {{WEBUI_NAME}}", "sk-1234": "", "Source": "Lä<PERSON><PERSON>", "Speech Playback Speed": "<PERSON><PERSON><PERSON><PERSON><PERSON> nopeus", "Speech recognition error: {{error}}": "Puheentunnistusvir<PERSON>: {{error}}", "Speech-to-Text Engine": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Stop": "Pysäytä", "Stop Sequence": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Stream Chat Response": "Streamaa keskusteluvastaus", "STT Model": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "STT Settings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as<PERSON>", "Success": "<PERSON><PERSON><PERSON>", "Successfully updated.": "<PERSON><PERSON><PERSON><PERSON><PERSON> onnistuneesti.", "Suggested prompts to get you started": "", "Support": "<PERSON><PERSON>", "Support this plugin:": "<PERSON>e tätä lisäosaa:", "Sync directory": "Synkron<PERSON><PERSON> hake<PERSON>o", "System": "Järjestelmä", "System Instructions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "System Prompt": "Järjestelmäkehote", "Tags Generation": "<PERSON><PERSON> luonti", "Tags Generation Prompt": "<PERSON><PERSON> luo<PERSON>", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting. (default: 1)": "Tail-free-otanta käytetään vähentämään vähemmän todennäköisten tokenien vaikutusta tulokseen. Korkeampi arvo (esim. 2,0) vähentää vaikutusta enemmän, kun taas arvo 1,0 poistaa tämän asetuksen käytöstä. (oletus: 1)", "Tap to interrupt": "Napauta keskeyttääksesi", "Tavily API Key": "Tavily API -avain", "Temperature": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Template": "<PERSON><PERSON>", "Temporary Chat": "Väliaikainen keskustelu", "Text Splitter": "<PERSON><PERSON><PERSON>", "Text-to-Speech Engine": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Tfs Z": "Tfs Z", "Thanks for your feedback!": "Ki<PERSON>s p<PERSON>utt<PERSON>i!", "The Application Account DN you bind with for search": "<PERSON><PERSON>a varten sidottu sovelluksen käyttäjätilin DN", "The base to search for users": "K<PERSON>yttäji<PERSON> haun perusta", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.  (Default: 512)": "<PERSON><PERSON><PERSON><PERSON> koko m<PERSON>, kuinka monta tekstipyyntöä käsitellään yhdessä kerralla. Suurempi erän koko voi parantaa mallin suorituskykyä ja nopeutta, mutta se vaatii myös enemmän muistia. (Oletus: 512)", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "Tämän lisäosan takana olevat kehittäjät ovat intohimoisia vapaaehtoisyhteisöstä. <PERSON><PERSON> koet tämän lisäosan hyödylliseksi, harkitse sen kehittämisen tukemista.", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "Arviointitulosluettelo perustuu Elo-luokitusjärjestelmään ja päivittyy reaaliajassa.", "The LDAP attribute that maps to the username that users use to sign in.": "LDAP-määrite, joka vastaa käyttäjien kirjautumiskäyttäjänimeä.", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "<PERSON><PERSON><PERSON><PERSON><PERSON> on tällä hetkellä beta-v<PERSON><PERSON><PERSON>, ja voimme säätää pisteytyksen laskentaa hienostaessamme algoritmia.", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "Enimmäistiedostokoko megata<PERSON>. <PERSON><PERSON> tied<PERSON>on koko ylittää tämän rajan, tied<PERSON><PERSON> ei ladata.", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "<PERSON><PERSON><PERSON> sallittu tiedostojen määrä käytettäväksi kerralla chatissa. <PERSON><PERSON> tiedostojen määrä ylittää tämän rajan, niitä ei ladata.", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "Pisteytyksen tulee olla arvo välillä 0,0 (0 %) ja 1,0 (100 %).", "The temperature of the model. Increasing the temperature will make the model answer more creatively. (Default: 0.8)": "<PERSON><PERSON> lämpö<PERSON>. Lämpötilan nostaminen saa mallin vastaamaan luovemmin. (Oletus: 0,8)", "Theme": "<PERSON><PERSON>", "Thinking...": "A<PERSON><PERSON><PERSON>...", "This action cannot be undone. Do you wish to continue?": "Tätä toimintoa ei voi peruuttaa. Haluatko jatkaa?", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "Tämä varmistaa, että arvokkaat keskustelusi tallennetaan turvallisesti backend-tietokantaasi. Kiitos!", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "Tämä on kokeellinen ominaisuus, se ei välttämättä toimi odotetulla tavalla ja se voi muuttua milloin tahansa.", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics. (Default: 24)": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> kontroll<PERSON>, kuinka monta tokenia säilytetään päivittäessä kontekstia. Esimerkiksi, jos asetetaan arvoksi 2, säilytetään viimeiset 2 keskustelukon-tekstin tokenia. Kontekstin säilyttäminen voi auttaa ylläpitämään keskustelun jatkuvuutta, mutta se voi vähentää kykyä vastata uusiin aiheisiin. (<PERSON><PERSON>: 24)", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.  (Default: 128)": "<PERSON><PERSON><PERSON><PERSON> as<PERSON> mä<PERSON>rittää mallin vastauksen enimmäistokenmäärän. Tämän rajan nostaminen mahdollistaa mallin antavan pidempiä vastauksia, mutta se voi myös lisätä epähyödyllisen tai epärelevantin sisällön todennäköisyyttä. (Oletus: 128)", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "Tämä vaihtoehto poistaa kaikki koko<PERSON>man n<PERSON> tiedostot ja korvaa ne uusilla ladatuilla tiedostoilla.", "This response was generated by \"{{model}}\"": "<PERSON><PERSON><PERSON><PERSON><PERSON> tuotti \"{{model}}\"", "This will delete": "<PERSON><PERSON><PERSON>ä poistaa", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "<PERSON><PERSON><PERSON><PERSON> poistaa <strong>{{NAME}}</strong> ja <strong>kaikki sen sis<PERSON><PERSON><PERSON>t</strong>.", "This will delete all models including custom models": "Tämä poistaa kaikki mallit mukaan lukien mukautetut mallit", "This will delete all models including custom models and cannot be undone.": "<PERSON><PERSON><PERSON><PERSON> poistaa kaikki mallit, mukaan lukien mukautetut mallit, eikä sitä voi peruuttaa.", "This will reset the knowledge base and sync all files. Do you wish to continue?": "<PERSON>ä<PERSON>ä nollaa tietokannan ja synkronoi kaikki tied<PERSON>ot. Haluatko jatkaa?", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "Tika Server URL vaaditaan.", "Tiktoken": "Tiktoken", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Vinkki: Päivitä useita muuttujapaikkoja peräkkäin painamalla tabulaattoria keskustelusyötteessä jokaisen korvauksen jälkeen.", "Title": "<PERSON><PERSON><PERSON><PERSON>", "Title (e.g. Tell me a fun fact)": "<PERSON><PERSON><PERSON><PERSON> (es<PERSON>. <PERSON><PERSON> fakta)", "Title Auto-Generation": "Otsikon automaattinen luonti", "Title cannot be an empty string.": "Otsikko ei voi olla tyhjä merkkijono.", "Title Generation Prompt": "Otsikon luontikehote", "TLS": "TLS", "To access the available model names for downloading,": "Päästäksesi käsiksi ladattavissa oleviin mallinimiin,", "To access the GGUF models available for downloading,": "Päästäksesi käsiksi ladattavissa oleviin GGUF-malleihin,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "Päästäksesi k<PERSON>tämään WebUI:ta, ota yhteyttä ylläpitäjään. Ylläpitäjät voivat hallita käyttäjien tiloja Ylläpitopaneelista.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "Liittääks<PERSON> tie<PERSON> tä<PERSON>, lis<PERSON><PERSON> ne ensin \"Tiet<PERSON><PERSON><PERSON>\"-työtilaan.", "To learn more about available endpoints, visit our documentation.": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "Yksityisyydensuojasi vuoksi palautteestasi jaetaan vain arvostelut, mallit<PERSON><PERSON><PERSON><PERSON>, tagit ja metadata - keskustelulokisi pysyvät yksityisinä eikä niitä sisällytetä.", "To select actions here, add them to the \"Functions\" workspace first.": "Valitaks<PERSON> toimintoja tässä, lisä<PERSON> ne ensin \"To<PERSON><PERSON>ot\"-työtilaan.", "To select filters here, add them to the \"Functions\" workspace first.": "Valitaksesi suodattimia tässä, lisä<PERSON> ne ensin \"<PERSON><PERSON><PERSON>ot\"-työtilaan.", "To select toolkits here, add them to the \"Tools\" workspace first.": "Valitaksesi työkalusettejä tässä, lisää ne ensin \"Työkalut\"-työtilaan.", "Toast notifications for new updates": "Ilmoituspopuppien näyttäminen uusista päivityksistä", "Today": "Tänää<PERSON>", "Toggle settings": "<PERSON><PERSON><PERSON>", "Toggle sidebar": "<PERSON><PERSON><PERSON>", "Token": "Token", "Tokens To Keep On Context Refresh (num_keep)": "Säilytettävät tokenit kontekstin päivityksessä (num_keep)", "Tool created successfully": "Työkalu luotu onnistuneesti", "Tool deleted successfully": "Työkalu poistettu onnistuneesti", "Tool Description": "Työkalun kuvaus", "Tool ID": "<PERSON><PERSON><PERSON><PERSON> tunnus", "Tool imported successfully": "Työkalu tuotu onnistuneesti", "Tool Name": "<PERSON><PERSON><PERSON><PERSON> nimi", "Tool updated successfully": "Työkalu päivitetty onnistuneesti", "Tools": "Työkalut", "Tools Access": "Työkalujen k<PERSON>öoikeudet", "Tools are a function calling system with arbitrary code execution": "Työkalut ovat toimintokutsuihin perustuva jär<PERSON><PERSON>lmä, joka sallii mi<PERSON> koodin su<PERSON>tta<PERSON>n", "Tools have a function calling system that allows arbitrary code execution": "Ty<PERSON><PERSON><PERSON>la on to<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> perustuva j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, joka sallii mi<PERSON> koodin su<PERSON>tta<PERSON>n", "Tools have a function calling system that allows arbitrary code execution.": "Työkalut sallivat mieli<PERSON>sen koodin suorittamisen toimintokutsuilla.", "Top K": "Top K", "Top P": "Top P", "Transformers": "Muunnokset", "Trouble accessing Ollama?": "Ongelmia Ollama-yhteydessä?", "TTS Model": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TTS Settings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>setukset", "TTS Voice": "Puhesynteesiääni", "Type": "Tyyppi", "Type Hugging Face Resolve (Download) URL": "<PERSON><PERSON><PERSON><PERSON> Face -resolve-latausosoite", "Uh-oh! There was an issue with the response.": "", "UI": "Käyttöliittymä", "Unarchive All": "<PERSON>ura kaikkien a<PERSON>istointi", "Unarchive All Archived Chats": "Pura kaikkien arkistoitujen keskustelujen arkistointi", "Unarchive Chat": "<PERSON>ura keskustelun arkistointi", "Unlock mysteries": "Selvitä arvoituksia", "Unpin": "Irrota kiinnitys", "Unravel secrets": "<PERSON><PERSON>", "Untagged": "<PERSON><PERSON> tageja", "Update": "Päivitä", "Update and Copy Link": "Päivitä ja kopioi linkki", "Update for the latest features and improvements.": "Päivitä uusim<PERSON>in ominaisuuksiin ja parannuksiin.", "Update password": "Päivitä sa<PERSON>", "Updated": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Updated at": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Updated At": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Upload": "Lataa", "Upload a GGUF model": "Lataa GGUF-malli", "Upload directory": "<PERSON><PERSON><PERSON><PERSON> hakemisto", "Upload files": "<PERSON><PERSON><PERSON>", "Upload Files": "<PERSON><PERSON><PERSON>", "Upload Pipeline": "<PERSON><PERSON><PERSON> putki", "Upload Progress": "<PERSON><PERSON><PERSON><PERSON> ed<PERSON><PERSON>", "URL": "URL", "URL Mode": "URL-tila", "Use '#' in the prompt input to load and include your knowledge.": "Käytä '#' -merk<PERSON>ä kehotekenttään ladataksesi ja sisällyttääksesi tietämystäsi.", "Use Gravatar": "Käytä Gravataria", "Use groups to group your users and assign permissions.": "Käytä ryhmiä jäsentääksesi käyttäjiä ja antaaksesi käyttöoikeuk<PERSON>.", "Use Initials": "Käytä alkukirjaimia", "use_mlock (Ollama)": "use_mlock (Ollama)", "use_mmap (Ollama)": "use_mmap (Ollama)", "user": "käyttäjä", "User": "Käyttäjä", "User location successfully retrieved.": "Käyttäjän sijainti haettu onnistuneesti.", "Username": "K<PERSON>yttäjät<PERSON>nus", "Users": "Käyttäjät", "Using the default arena model with all models. Click the plus button to add custom models.": "Käytetään o<PERSON>-mallia kaikkien mallien kanssa. Napsauta plus-painiketta lisätäks<PERSON> mukautettuja malleja.", "Utilize": "Hyödynnä", "Valid time units:": "Kelvolliset aikayksiköt:", "Valves": "Venttiilit", "Valves updated": "Ventti<PERSON>t päiv<PERSON>tty", "Valves updated successfully": "Venttiilit päivitetty onnistuneesti", "variable": "mu<PERSON><PERSON><PERSON>", "variable to have them replaced with clipboard content.": "muuttuja korvataan leikepöydän sisällöllä.", "Version": "Versio", "Version {{selectedVersion}} of {{totalVersions}}": "Versio {{selectedVersion}} / {{totalVersions}}", "Very bad": "", "View Replies": "", "Visibility": "Näkyvyys", "Voice": "<PERSON><PERSON><PERSON>", "Voice Input": "Äänitulolaitteen käyttö", "Warning": "Varo<PERSON><PERSON>", "Warning:": "Varoitus:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "Varoitus: <PERSON><PERSON><PERSON><PERSON><PERSON> kä<PERSON>öönotto sallii käyttäjien ladata mielivaltaista koodia palvelimelle.", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Varoitus: <PERSON><PERSON> p<PERSON> tai vaihdat upotus<PERSON>, sinun on tuotava kaikki asia<PERSON><PERSON><PERSON><PERSON> u<PERSON>.", "Web": "Web", "Web API": "Web-API", "Web Loader Settings": "Web Loader -asetukset", "Web Search": "Web-haku", "Web Search Engine": "Web-hakukone", "Web Search Query Generation": "Web-haun kys<PERSON> luonti", "Webhook URL": "Webhook-URL", "WebUI Settings": "WebUI-asetukset", "WebUI URL": "", "WebUI will make requests to \"{{url}}/api/chat\"": "WebUI lähettää pyyntöjä osoitteeseen \"{{url}}/api/chat\"", "WebUI will make requests to \"{{url}}/chat/completions\"": "WebUI lähettää pyyntöjä osoitteeseen \"{{url}}/chat/completions\"", "What are you trying to achieve?": "Mitä yrität saavuttaa?", "What are you working on?": "<PERSON><PERSON> olet t<PERSON>ntelemässä?", "What didn't you like about this response?": "", "What’s New in": "", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "<PERSON><PERSON> k<PERSON><PERSON>, malli vastaa jokaiseen chatviestiin reaaliajas<PERSON>, tuottaen vastauksen heti kun käyttäjä lähettää viestin. Tämä tila on hyödyllinen reaaliaikaisissa chat-sovelluksissa, mutta voi vaikuttaa suorituskykyyn hitaammilla laitteistoilla.", "wherever you are": "miss<PERSON> ta<PERSON><PERSON>", "Whisper (Local)": "Whisper (paikallinen)", "Widescreen Mode": "Laajakuvatila", "Won": "<PERSON><PERSON><PERSON>", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text. (Default: 0.9)": "Toimii yhdessä top-k:n kanssa. Korkeampi arvo (esim. 0,95) tuottaa monipuolisempaa tekstiä, kun taas alhaisempi arvo (esim. 0,5) tuottaa keskittyneempää ja konservatiivisempaa tekstiä. (Oletus: 0,9)", "Workspace": "<PERSON><PERSON><PERSON><PERSON>", "Workspace Permissions": "<PERSON><PERSON><PERSON><PERSON>", "Write a prompt suggestion (e.g. Who are you?)": "<PERSON><PERSON><PERSON><PERSON> keh<PERSON> ehdo<PERSON> (esim. <PERSON><PERSON> olet?)", "Write a summary in 50 words that summarizes [topic or keyword].": "Kirjoita 50 sanan y<PERSON>, joka tii<PERSON> [aihe tai ava<PERSON>].", "Write something...": "<PERSON><PERSON><PERSON><PERSON> jotain...", "Write your model template content here": "<PERSON><PERSON><PERSON><PERSON> mallisi mallinnesisältö tähän", "Yesterday": "<PERSON><PERSON><PERSON>", "You": "Sin<PERSON>", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "<PERSON>oit keskustella enintään {{maxCount}} tiedoston kanssa ker<PERSON>.", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "Voit personoida vuorovaikutustasi LLM-ohjelmien kanssa lisäämällä muistoja 'Hallitse'-pain<PERSON><PERSON><PERSON> kautta, jolloin ne ovat hyödyllisempiä ja räätälöityjä sinua varten.", "You cannot upload an empty file.": "Et voi ladata tyhjää tiedostoa.", "You have no archived conversations.": "Sinulla ei ole arkistoituja keskusteluja.", "You have shared this chat": "<PERSON><PERSON> j<PERSON> tämän keskustelun", "You're a helpful assistant.": "<PERSON><PERSON> a<PERSON> a<PERSON>.", "Your account status is currently pending activation.": "Tilisi tila on tällä hetkellä odottaa aktivointia.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "<PERSON><PERSON> pan<PERSON> menee suoraan lisäosan kehittäjälle; Open WebUI ei pidätä prosenttiosuutta. Valittu rahoitusalusta voi kuitenkin periä omia maksujaan.", "Youtube": "YouTube", "Youtube Loader Settings": "YouTube Loader -asetukset"}