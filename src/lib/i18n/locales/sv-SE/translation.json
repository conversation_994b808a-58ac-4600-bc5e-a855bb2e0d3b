{"-1 for no limit, or a positive integer for a specific limit": "", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' eller '-1' för inget utg<PERSON>datum", "(e.g. `sh webui.sh --api --api-auth username_password`)": "", "(e.g. `sh webui.sh --api`)": "(t.ex. `sh webui.sh --api`)", "(latest)": "(senaste)", "{{ models }}": "{{ modeller }}", "{{COUNT}} Replies": "", "{{user}}'s Chats": "{{user}}s Chats", "{{webUIName}} Backend Required": "{{webUIName}} Backend krävs", "*Prompt node ID(s) are required for image generation": "", "A new version (v{{LATEST_VERSION}}) is now available.": "", "A task model is used when performing tasks such as generating titles for chats and web search queries": "En uppgiftsmodell används när du utför uppgifter som att generera titlar för chattar och webbsökningsfrågor", "a user": "en användare", "About": "Om", "Access": "", "Access Control": "", "Accessible to all users": "", "Account": "Ko<PERSON>", "Account Activation Pending": "Kontoaktivering väntar", "Actions": "", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "", "Active Users": "Aktiva användare", "Add": "<PERSON><PERSON><PERSON> till", "Add a model ID": "", "Add a short description about what this model does": "Lägg till en kort beskrivning av vad den här modellen gör", "Add a tag": "Lägg till en tagg", "Add Arena Model": "", "Add Connection": "", "Add Content": "", "Add content here": "", "Add custom prompt": "Lägg till en anpassad instruktion", "Add Files": "Lägg till filer", "Add Group": "", "Add Memory": "Lägg till minne", "Add Model": "Lägg till modell", "Add Reaction": "", "Add Tag": "", "Add Tags": "Lägg till taggar", "Add text content": "", "Add User": "Lägg till användare", "Add User Group": "", "Adjusting these settings will apply changes universally to all users.": "Justering av dessa inställningar kommer att tillämpa ändringar universellt för alla användare.", "admin": "administratör", "Admin": "Admin", "Admin Panel": "Administrationspanel", "Admin Settings": "Administratörsinställningar", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "Administratörer har tillgång till alla verktyg hela tiden, medan användare behöver verktyg som tilldelas per modell i arbetsytan.", "Advanced Parameters": "Avancerade parametrar", "Advanced Params": "Avancerade parametrar", "All Documents": "Alla dokument", "All models deleted successfully": "", "Allow Chat Delete": "", "Allow Chat Deletion": "<PERSON><PERSON><PERSON> chatt<PERSON>", "Allow Chat Edit": "", "Allow File Upload": "", "Allow non-local voices": "<PERSON><PERSON><PERSON> i<PERSON>-lo<PERSON>a rö<PERSON>", "Allow Temporary Chat": "", "Allow User Location": "", "Allow Voice Interruption in Call": "", "Allowed Endpoints": "", "Already have an account?": "Har du redan ett konto?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out. (Default: 0.0)": "", "an assistant": "en assistent", "and": "och", "and {{COUNT}} more": "", "and create a new shared link.": "och skapa en ny delad länk.", "API Base URL": "API-bas-URL", "API Key": "API-nyckel", "API Key created.": "API-nyckel skapad.", "API Key Endpoint Restrictions": "", "API keys": "API-nycklar", "Application DN": "", "Application DN Password": "", "applies to all users with the \"user\" role": "", "April": "april", "Archive": "Arkiv", "Archive All Chats": "Arkivera alla chattar", "Archived Chats": "Arkiverade chattar", "archived-chat-export": "", "Are you sure you want to delete this channel?": "", "Are you sure you want to delete this message?": "", "Are you sure you want to unarchive all archived chats?": "", "Are you sure?": "<PERSON>r du säker?", "Arena Models": "", "Artifacts": "", "Ask a question": "", "Assistant": "", "Attach file": "Bifoga fil", "Attribute for Username": "", "Audio": "<PERSON><PERSON><PERSON>", "August": "augusti", "Authenticate": "", "Auto-Copy Response to Clipboard": "Svara AutoCopy till urklipp", "Auto-playback response": "Automatisk uppspelning", "Autocomplete Generation": "", "Autocomplete Generation Input Max Length": "", "Automatic1111": "", "AUTOMATIC1111 Api Auth String": "", "AUTOMATIC1111 Base URL": "AUTOMATIC1111 bas-URL", "AUTOMATIC1111 Base URL is required.": "AUTOMATIC1111 bas-URL krävs.", "Available list": "", "available!": "tillgänglig!", "Azure AI Speech": "", "Azure Region": "", "Back": "Tillbaka", "Bad": "", "Bad Response": "<PERSON><PERSON><PERSON><PERSON> respons", "Banners": "Banners", "Base Model (From)": "<PERSON><PERSON><PERSON> (Från)", "Batch Size (num_batch)": "Batchstorlek (num_batch)", "before": "<PERSON><PERSON><PERSON>", "Beta": "", "BETA": "", "Bing Search V7 Endpoint": "", "Bing Search V7 Subscription Key": "", "Brave Search API Key": "API-nyckel för Brave Search", "By {{name}}": "", "Bypass SSL verification for Websites": "Kringgå SSL-verifiering för webbplatser", "Call": "<PERSON><PERSON>", "Call feature is not supported when using Web STT engine": "Samtalsfunktionen är inte kompatibel med Web Tal-till-text motor", "Camera": "<PERSON><PERSON><PERSON>", "Cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Capabilities": "Kapa<PERSON>ter", "Capture": "", "Certificate Path": "", "Change Password": "<PERSON><PERSON>", "Channel Name": "", "Channels": "", "Character": "", "Character limit for autocomplete generation input": "", "Chart new frontiers": "", "Chat": "<PERSON><PERSON>", "Chat Background Image": "", "Chat Bubble UI": "Chatbubblar UI", "Chat Controls": "", "Chat direction": "Chattriktning", "Chat Overview": "", "Chat Permissions": "", "Chat Tags Auto-Generation": "", "Chats": "Chattar", "Check Again": "Kontrollera igen", "Check for updates": "<PERSON><PERSON><PERSON> efter upp<PERSON><PERSON><PERSON>", "Checking for updates...": "<PERSON><PERSON><PERSON> efter uppdateringar...", "Choose a model before saving...": "V<PERSON><PERSON><PERSON> en modell innan du sparar...", "Chunk Overlap": "Överlappning", "Chunk Params": "Chunk-parametrar", "Chunk Size": "Chunk-storlek", "Ciphers": "", "Citation": "Citat", "Clear memory": "<PERSON><PERSON> minnet", "click here": "", "Click here for filter guides.": "", "Click here for help.": "<PERSON><PERSON><PERSON> här för hj<PERSON><PERSON>.", "Click here to": "<PERSON><PERSON><PERSON> här för att", "Click here to download user import template file.": "", "Click here to learn more about faster-whisper and see the available models.": "", "Click here to select": "<PERSON><PERSON><PERSON> här för att välja", "Click here to select a csv file.": "<PERSON><PERSON><PERSON> här för att välja en csv-fil.", "Click here to select a py file.": "<PERSON><PERSON><PERSON> här för att välja en python-fil.", "Click here to upload a workflow.json file.": "", "click here.": "k<PERSON>a här.", "Click on the user role button to change a user's role.": "<PERSON><PERSON><PERSON> på knappen för användarroll för att ändra en användares roll.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "", "Clone": "Klon", "Close": "Stäng", "Code execution": "", "Code formatted successfully": "", "Collection": "<PERSON><PERSON>", "Color": "", "ComfyUI": "ComfyUI", "ComfyUI API Key": "", "ComfyUI Base URL": "ComfyUI Base URL", "ComfyUI Base URL is required.": "ComfyUI Base URL krävs.", "ComfyUI Workflow": "", "ComfyUI Workflow Nodes": "", "Command": "Kommando", "Completions": "", "Concurrent Requests": "<PERSON><PERSON><PERSON> an<PERSON>", "Configure": "", "Configure Models": "", "Confirm": "", "Confirm Password": "Bekräfta lösenord", "Confirm your action": "", "Confirm your new password": "", "Connections": "Anslutningar", "Contact Admin for WebUI Access": "Kontakta administratören för att få åtkomst till WebUI", "Content": "<PERSON><PERSON><PERSON><PERSON>", "Content Extraction": "", "Context Length": "Kontextlängd", "Continue Response": "Fortsätt svar", "Continue with {{provider}}": "", "Continue with Email": "", "Continue with LDAP": "", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "", "Controls": "", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text. (Default: 5.0)": "", "Copied": "", "Copied shared chat URL to clipboard!": "<PERSON><PERSON><PERSON> delad chatt-URL till urklipp!", "Copied to clipboard": "", "Copy": "<PERSON><PERSON><PERSON>", "Copy last code block": "<PERSON><PERSON><PERSON> sista kodblock", "Copy last response": "<PERSON><PERSON><PERSON> sista svar", "Copy Link": "<PERSON><PERSON><PERSON> länk", "Copy to clipboard": "", "Copying to clipboard was successful!": "<PERSON><PERSON>ring till urk<PERSON><PERSON> lyck<PERSON>!", "Create": "", "Create a knowledge base": "", "Create a model": "Skapa en modell", "Create Account": "Skapa konto", "Create Admin Account": "", "Create Channel": "", "Create Group": "", "Create Knowledge": "", "Create new key": "Skapa ny nyckel", "Create new secret key": "Skapa ny hemlig nyckel", "Created at": "Skapad", "Created At": "Skapad", "Created by": "", "CSV Import": "", "Current Model": "Aktuell modell", "Current Password": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Custom": "Anpassad", "Dark": "M<PERSON><PERSON>", "Database": "Databas", "December": "december", "Default": "Standard", "Default (Open AI)": "", "Default (SentenceTransformers)": "Standard (SentenceTransformers)", "Default Model": "Standardmodell", "Default model updated": "Standardmodell uppdaterad", "Default Models": "", "Default permissions": "", "Default permissions updated successfully": "", "Default Prompt Suggestions": "Standardinstruktionsförslag", "Default to 389 or 636 if TLS is enabled": "", "Default to ALL": "", "Default User Role": "Standardanvändarroll", "Delete": "<PERSON><PERSON><PERSON>", "Delete a model": "Ta bort en modell", "Delete All Chats": "Ta bort alla chattar", "Delete All Models": "", "Delete chat": "<PERSON><PERSON><PERSON> chatt", "Delete Chat": "<PERSON><PERSON><PERSON> chatt", "Delete chat?": "", "Delete folder?": "", "Delete function?": "", "Delete Message": "", "Delete prompt?": "", "delete this link": "radera denna länk", "Delete tool?": "", "Delete User": "<PERSON><PERSON><PERSON>", "Deleted {{deleteModelTag}}": "Raderad {{deleteModelTag}}", "Deleted {{name}}": "Borttagen {{name}}", "Deleted User": "", "Describe your knowledge base and objectives": "", "Description": "Beskrivning", "Disabled": "", "Discover a function": "", "Discover a model": "<PERSON><PERSON><PERSON><PERSON> en modell", "Discover a prompt": "Upptäck en instruktion", "Discover a tool": "", "Discover wonders": "", "Discover, download, and explore custom functions": "", "Discover, download, and explore custom prompts": "<PERSON><PERSON><PERSON><PERSON>, ladda ner och utforska anpassade instruktioner", "Discover, download, and explore custom tools": "", "Discover, download, and explore model presets": "<PERSON><PERSON><PERSON><PERSON>, ladda ner och utforska modellförinställningar", "Dismissible": "<PERSON>n st<PERSON>", "Display": "", "Display Emoji in Call": "Visa Emoji under samtal", "Display the username instead of You in the Chat": "Visa användarnamnet istället för du i chatten", "Displays citations in the response": "", "Dive into knowledge": "", "Do not install functions from sources you do not fully trust.": "", "Do not install tools from sources you do not fully trust.": "", "Document": "Dokument", "Documentation": "Dokumentation", "Documents": "Dokument", "does not make any external connections, and your data stays securely on your locally hosted server.": "gör inga externa anslut<PERSON>, och dina data förblir säkra på din lokalt värdade server.", "Don't have an account?": "Har du inget konto?", "don't install random functions from sources you don't trust.": "", "don't install random tools from sources you don't trust.": "", "Done": "", "Download": "Ladda ner", "Download canceled": "Nedladdning avbruten", "Download Database": "Ladda ner databas", "Drag and drop a file to upload or select a file to view": "", "Draw": "", "Drop any files here to add to the conversation": "<PERSON><PERSON><PERSON><PERSON> filer här för att lägga till i samtalet", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "t.ex. '30s', '10m'. Gil<PERSON>ga tidsenheter är 's', 'm', 'h'.", "e.g. A filter to remove profanity from text": "", "e.g. My Filter": "", "e.g. My Tools": "", "e.g. my_filter": "", "e.g. my_tools": "", "e.g. Tools for performing various operations": "", "Edit": "Rediger<PERSON>", "Edit Arena Model": "", "Edit Channel": "", "Edit Connection": "", "Edit Default Permissions": "", "Edit Memory": "", "Edit User": "<PERSON>igera an<PERSON>", "Edit User Group": "", "ElevenLabs": "", "Email": "E-post", "Embark on adventures": "", "Embedding Batch Size": "Batchstorlek för inbäddning", "Embedding Model": "Inbäddningsmodell", "Embedding Model Engine": "Motor för inbäddningsmodell", "Embedding model set to \"{{embedding_model}}\"": "Inbäddningsmodell inställd på \"{{embedding_model}}\"", "Enable API Key": "", "Enable autocomplete generation for chat messages": "", "Enable Community Sharing": "Aktivera community-delning", "Enable Google Drive": "", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "", "Enable Message Rating": "", "Enable Mirostat sampling for controlling perplexity. (Default: 0, 0 = Disabled, 1 = Mirostat, 2 = Mirostat 2.0)": "", "Enable New Sign Ups": "Aktivera nya registreringar", "Enable Web Search": "Aktivera webbsökning", "Enabled": "", "Engine": "", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "Se till att din CSV-fil innehåller fyra kolumner i denna ordning: Name, Email, Password, Role.", "Enter {{role}} message here": "Skriv {{role}} meddela<PERSON> här", "Enter a detail about yourself for your LLMs to recall": "Skriv en detalj om dig själv för att dina LLMs ska komma ihåg", "Enter api auth string (e.g. username:password)": "", "Enter Application DN": "", "Enter Application DN Password": "", "Enter Bing Search V7 Endpoint": "", "Enter Bing Search V7 Subscription Key": "", "Enter Brave Search API Key": "Ange API-nyckel för Brave Search", "Enter certificate path": "", "Enter CFG Scale (e.g. 7.0)": "", "Enter Chunk Overlap": "<PERSON><PERSON>", "Enter Chunk Size": "<PERSON><PERSON>", "Enter description": "", "Enter Github Raw URL": "<PERSON><PERSON> Raw URL", "Enter Google PSE API Key": "Ange Google PSE API-nyckel", "Enter Google PSE Engine Id": "Ange Google PSE Engine Id", "Enter Image Size (e.g. 512x512)": "<PERSON><PERSON> (t.ex. 512x512)", "Enter Jina API Key": "", "Enter Kagi Search API Key": "", "Enter language codes": "Skriv språkkoder", "Enter Model ID": "", "Enter model tag (e.g. {{modelTag}})": "<PERSON><PERSON> model<PERSON>gg (t.ex. {{modelTag}})", "Enter Mojeek Search API Key": "", "Enter name or email": "", "Enter Number of Steps (e.g. 50)": "<PERSON><PERSON> antal steg (t.ex. 50)", "Enter proxy URL (e.g. **************************:port)": "", "Enter Sampler (e.g. Euler a)": "", "Enter Scheduler (e.g. Karras)": "", "Enter Score": "<PERSON><PERSON> bet<PERSON>g", "Enter SearchApi API Key": "", "Enter SearchApi Engine": "", "Enter Searxng Query URL": "Ange Searxng Query URL", "Enter Seed": "", "Enter Serper API Key": "<PERSON><PERSON>-nyckel", "Enter Serply API Key": "Ange Serply API-nyckel", "Enter Serpstack API Key": "Ange <PERSON>pstack API-nyckel", "Enter server host": "", "Enter server label": "", "Enter server port": "", "Enter stop sequence": "<PERSON><PERSON>", "Enter system prompt": "", "Enter Tavily API Key": "", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter Tika Server URL": "", "Enter Top K": "Ange Top K", "Enter URL (e.g. http://127.0.0.1:7860/)": "<PERSON>e URL (t.ex. http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "Ange URL (t.ex. http://localhost:11434)", "Enter your current password": "", "Enter Your Email": "Ange din e-post", "Enter Your Full Name": "<PERSON><PERSON> ditt fullständiga namn", "Enter your message": "", "Enter your new password": "", "Enter Your Password": "<PERSON><PERSON> l<PERSON>", "Enter your prompt": "", "Enter Your Role": "Ange din roll", "Enter Your Username": "", "Enter your webhook URL": "", "Error": "<PERSON><PERSON>", "ERROR": "", "Error accessing Google Drive: {{error}}": "", "Error uploading file: {{error}}": "", "Evaluations": "", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "", "Example: ALL": "", "Example: ou=users,dc=foo,dc=example": "", "Example: sAMAccountName or uid or userPrincipalName": "", "Exclude": "", "Experimental": "Experimentell", "Explore the cosmos": "", "Export": "Export", "Export All Archived Chats": "", "Export All Chats (All Users)": "Exportera alla chattar (alla användare)", "Export chat (.json)": "<PERSON><PERSON>a chatt (.json)", "Export Chats": "Exportera chattar", "Export Config to JSON File": "", "Export Functions": "", "Export Models": "Exportera modeller", "Export Presets": "", "Export Prompts": "Exportera instruktioner", "Export to CSV": "", "Export Tools": "Exportera verktyg", "External Models": "Externa modeller", "Extremely bad": "", "Failed to add file.": "", "Failed to create API Key.": "<PERSON><PERSON><PERSON><PERSON> med att skapa API-nyckel.", "Failed to read clipboard contents": "Misslyckades med att läsa urklippsinnehåll", "Failed to save models configuration": "", "Failed to update settings": "Misslyckades med att uppdatera inställningarna", "February": "februari", "Feedback History": "", "Feedbacks": "", "File": "", "File added successfully.": "", "File content updated successfully.": "", "File Mode": "Fil-läge", "File not found.": "Fil hittades inte.", "File removed successfully.": "", "File size should not exceed {{maxSize}} MB.": "", "File uploaded successfully": "", "Files": "", "Filter is now globally disabled": "", "Filter is now globally enabled": "", "Filters": "", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Fingeravtrycksmanipulering upptäckt: Kan inte använda initialer som avatar. Återställning till standardprofilbild.", "Fluidly stream large external response chunks": "Strömma flytande stora externa svarschunks", "Focus chat input": "Fokusera på chattfältet", "Folder deleted successfully": "", "Folder name cannot be empty": "", "Folder name cannot be empty.": "", "Folder name updated successfully": "", "Forge new paths": "", "Form": "", "Format your variables using brackets like this:": "", "Frequency Penalty": "<PERSON><PERSON><PERSON> för f<PERSON>", "Function": "", "Function created successfully": "", "Function deleted successfully": "", "Function Description": "", "Function ID": "", "Function is now globally disabled": "", "Function is now globally enabled": "", "Function Name": "", "Function updated successfully": "", "Functions": "", "Functions allow arbitrary code execution": "", "Functions allow arbitrary code execution.": "", "Functions imported successfully": "", "General": "Allmän", "General Settings": "Allmänna inställningar", "Generate Image": "Generera bild", "Generating search query": "<PERSON><PERSON><PERSON>", "Get started": "", "Get started with {{WEBUI_NAME}}": "", "Global": "", "Good Response": "<PERSON><PERSON> <PERSON>var", "Google Drive": "", "Google PSE API Key": "Google PSE API-nyckel", "Google PSE Engine Id": "Google PSE Engine Id", "Group created successfully": "", "Group deleted successfully": "", "Group Description": "", "Group Name": "", "Group updated successfully": "", "Groups": "", "GSA Chat can make mistakes. Review all responses for accuracy.": "", "h:mm a": "h:mm a", "Haptic Feedback": "", "Harmful or offensive": "", "has no conversations.": "har inga samtal.", "Hello, {{name}}": "Hej, {{name}}", "Help": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Help us create the best community leaderboard by sharing your feedback history!": "", "Hex Color": "", "Hex Color - Leave empty for default color": "", "Hide": "<PERSON><PERSON><PERSON><PERSON>", "Host": "", "How can I help you today?": "Hur kan jag hjälpa dig idag?", "How would you rate this response?": "", "Hybrid Search": "Hybrid sökning", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "", "ID": "", "Ignite curiosity": "", "Image Compression": "", "Image Generation (Experimental)": "<PERSON><PERSON><PERSON><PERSON><PERSON> (experimentell)", "Image Generation Engine": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Image Max Compression Size": "", "Image Settings": "Bildinställningar", "Images": "Bilder", "Import Chats": "Importera chattar", "Import Config from JSON File": "", "Import Functions": "", "Import Models": "Importera modeller", "Import Presets": "", "Import Prompts": "Importera instruktioner", "Import Tools": "Importera verktyg", "Include": "", "Include `--api-auth` flag when running stable-diffusion-webui": "", "Include `--api` flag when running stable-diffusion-webui": "Ink<PERSON>ra flaggan `--api` när du kör stable-diffusion-webui", "Incomplete response": "", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive. (Default: 0.1)": "", "Info": "Information", "Input commands": "Indatakommandon", "Install from Github URL": "Installera fr<PERSON><PERSON>-URL", "Instant Auto-Send After Voice Transcription": "Skicka automatiskt efter rösttranskribering", "Interface": "Gränssnitt", "Invalid file format.": "", "Invalid Tag": "Ogi<PERSON><PERSON> tagg", "is typing...": "", "January": "januari", "Jina API Key": "", "join our Discord for help.": "gå med i vår Discord för hjälp.", "JSON": "JSON", "JSON Preview": "Förhandsversion av JSON", "July": "juli", "June": "juni", "JWT Expiration": "JWT-utgångsdatum", "JWT Token": "JWT-token", "Kagi Search API Key": "", "Keep Alive": "Keep Alive", "Key": "", "Keyboard shortcuts": "Tangentbordsgenvägar", "Knowledge": "Kunskap", "Knowledge Access": "", "Knowledge created successfully.": "", "Knowledge deleted successfully.": "", "Knowledge reset successfully.": "", "Knowledge updated successfully": "", "Label": "", "Landing Page Mode": "", "Language": "Språk", "Last Active": "Senast aktiv", "Last Modified": "", "Last reply": "", "Latest users": "", "LDAP": "", "LDAP server updated": "", "Leaderboard": "", "Leave empty for unlimited": "", "Leave empty to include all models from \"{{URL}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{URL}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "", "Leave empty to use the default prompt, or enter a custom prompt": "", "Light": "<PERSON><PERSON><PERSON>", "Listening...": "Lyssnar...", "Local": "", "Local Models": "Lokala modeller", "Lost": "", "LTR": "LTR", "Made by OpenWebUI Community": "Skapad av OpenWebUI Community", "Make sure to enclose them with": "Se till att bifoga dem med", "Make sure to export a workflow.json file as API format from ComfyUI.": "", "Manage": "Hantera", "Manage Arena Models": "", "Manage Ollama": "", "Manage Ollama API Connections": "", "Manage OpenAI API Connections": "", "Manage Pipelines": "<PERSON><PERSON>", "March": "mars", "Max Tokens (num_predict)": "Maximalt antal tokens (num_predict)", "Max Upload Count": "", "Max Upload Size": "", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Högst 3 modeller kan laddas ner samtidigt. Vänligen försök igen senare.", "May": "maj", "Memories accessible by LLMs will be shown here.": "Minnen som LLM:er kan komma åt visas här.", "Memory": "<PERSON><PERSON>", "Memory added successfully": "", "Memory cleared successfully": "", "Memory deleted successfully": "", "Memory updated successfully": "", "Merge Responses": "", "Message rating should be enabled to use this feature": "", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "Meddelanden du skickar efter att ha skapat din länk kommer inte att delas. Användare med URL:en kommer att kunna se delad chatt.", "Min P": "", "Minimum Score": "Tröskel", "Mirostat": "Mirostat", "Mirostat Eta": "Mirostat Eta", "Mirostat Tau": "Mirostat Tau", "MMMM DD, YYYY": "MMMM DD, YYYY", "MMMM DD, YYYY HH:mm": "MMMM DD, YYYY HH:mm", "MMMM DD, YYYY hh:mm:ss A": "", "Model": "", "Model '{{modelName}}' has been successfully downloaded.": "Modellen '{{modelName}}' har laddats ner framgångsrikt.", "Model '{{modelTag}}' is already in queue for downloading.": "Modellen '{{modelTag}}' är redan i kö för nedl<PERSON>dning.", "Model {{modelId}} not found": "Modell {{modelId}} hittades inte", "Model {{modelName}} is not vision capable": "Modellen {{modelName}} är inte synkapabel", "Model {{name}} is now {{status}}": "<PERSON>len {{name}} är nu {{status}}", "Model accepts image inputs": "", "Model created successfully!": "", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Modellens filsystemväg upptäckt. Modellens kortnamn krävs för uppdate<PERSON>, kan inte fortsätta.", "Model Filtering": "", "Model ID": "Modell-ID", "Model IDs": "", "Model Name": "", "Model not selected": "Modell inte vald", "Model Params": "Modell Params", "Model Permissions": "", "Model updated successfully": "", "Modelfile Content": "Model<PERSON><PERSON>s innehåll", "Models": "Modeller", "Models Access": "", "Models configuration saved successfully": "", "Mojeek Search API Key": "", "more": "", "More": "<PERSON><PERSON>", "Name": "<PERSON><PERSON>", "Name your knowledge base": "", "New Chat": "<PERSON><PERSON> chatt", "New folder": "", "New Password": "Nytt lösenord", "new-channel": "", "No content found": "", "No content to speak": "", "No distance available": "", "No feedbacks found": "", "No file selected": "", "No files found.": "", "No groups with access, add a group to grant access": "", "No HTML, CSS, or JavaScript content found.": "", "No knowledge found": "", "No model IDs": "", "No models found": "", "No models selected": "", "No results found": "Inga resultat hittades", "No search query generated": "<PERSON><PERSON> sökfrå<PERSON> genererad", "No source available": "Ingen tillgänglig källa", "No users were found.": "", "No valves to update": "", "None": "Ingen", "Not accurate": "", "Not relevant": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Obs: Om du anger en tröskel kommer sökningen endast att returnera dokument med ett betyg som är större än eller lika med tröskeln.", "Notes": "", "Notification Sound": "", "Notification Webhook": "", "Notifications": "Notifikationer", "November": "november", "num_gpu (Ollama)": "", "num_thread (Ollama)": "num_thread (Ollama)", "OAuth ID": "", "October": "oktober", "Off": "Av", "Okay, Let's Go!": "<PERSON><PERSON>, nu kör vi!", "OLED Dark": "<PERSON><PERSON><PERSON> (OLED)", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API disabled": "Ollama API inaktiverat", "Ollama API settings updated": "", "Ollama Version": "Ollama-version", "On": "På", "Only alphanumeric characters and hyphens are allowed": "", "Only alphanumeric characters and hyphens are allowed in the command string.": "Endast alfanumeriska tecken och bindestreck är tillåtna i kommandosträngen.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "", "Only select users and groups with permission can access": "", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Hoppsan! Det ser ut som om URL:en är ogiltig. Dubbelkolla gärna och försök igen.", "Oops! There are files still uploading. Please wait for the upload to complete.": "", "Oops! There was an error in the previous response.": "", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Hoppsan! Du använder en ej stödd metod (endast frontend). Vänligen servera WebUI från backend.", "Open in full screen": "", "Open new chat": "Öppna ny chatt", "Open WebUI uses faster-whisper internally.": "", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "", "OpenAI": "OpenAI", "OpenAI API": "OpenAI API", "OpenAI API Config": "OpenAI API-konfig", "OpenAI API Key is required.": "OpenAI API-nyckel krävs.", "OpenAI API settings updated": "", "OpenAI URL/Key required.": "OpenAI-URL/nyckel krävs.", "or": "eller", "Organize your users": "", "OUTPUT": "", "Output format": "", "Overview": "", "page": "", "Password": "L<PERSON>senord", "Paste Large Text as File": "", "PDF document (.pdf)": "PDF-dokument (.pdf)", "PDF Extract Images (OCR)": "PDF <PERSON><PERSON><PERSON> bilder (OCR)", "pending": "väntande", "Permission denied when accessing media devices": "Nekad behörighet vid åtkomst till mediaenheter", "Permission denied when accessing microphone": "Nekad behörighet vid åtkomst till mikrofon", "Permission denied when accessing microphone: {{error}}": "Tillstånd nekades vid åtkomst till mikrofon: {{error}}", "Permissions": "", "Personalization": "Personalisering", "Pin": "", "Pinned": "", "Pioneer insights": "", "Pipeline deleted successfully": "", "Pipeline downloaded successfully": "", "Pipelines": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Pipelines Not Detected": "", "Pipelines Valves": "<PERSON><PERSON><PERSON> f<PERSON><PERSON>", "Plain text (.txt)": "Text (.txt)", "Playground": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Please carefully review the following warnings:": "", "Please enter a prompt": "", "Please fill in all fields.": "", "Please select a model first.": "", "Port": "", "Prefix ID": "", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "", "Previous 30 days": "Föregående 30 dagar", "Previous 7 days": "Föregående 7 dagar", "Profile Image": "Profilbild", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Instruktion (t.ex. Berätta en kuriosa om Romerska Imperiet)", "Prompt Content": "Instruktionens innehåll", "Prompt created successfully": "", "Prompt suggestions": "Instruktionsförslag", "Prompt updated successfully": "", "Prompts": "Instruktioner", "Prompts Access": "", "Provide any specific details": "", "Proxy URL": "", "Pull \"{{searchValue}}\" from Ollama.com": "<PERSON>dda ner \"{{searchValue}}\" från <PERSON>.com", "Pull a model from Ollama.com": "Ladda ner en modell från Ollama.com", "Query Generation Prompt": "", "Query Params": "Inställningar för s<PERSON>frå<PERSON>", "RAG Template": "RAG-mall", "Rating": "", "Re-rank models by topic similarity": "", "Read Aloud": "<PERSON><PERSON><PERSON>", "Record voice": "Spela in röst", "Redirecting you to OpenWebUI Community": "Omdirigerar dig till OpenWebUI Community", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative. (Default: 40)": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "Referera till dig själv som \"Användare\" (t.ex. \"Användaren lär sig <PERSON>ka\")", "References from": "", "Refresh Token Expiration": "", "Regenerate": "Regenerera", "Release Notes": "Versionsinformation", "Relevance": "", "Remove": "<PERSON> bort", "Remove Model": "Ta bort modell", "Rename": "Byt namn", "Reorder Models": "", "Repeat Last N": "Upprepa senaste N", "Reply in Thread": "", "Request Mode": "Frågeläge", "Reranking Model": "Reranking modell", "Reranking model disabled": "Reranking modell inaktiverad", "Reranking model set to \"{{reranking_model}}\"": "Reranking modell inställd på \"{{reranking_model}}\"", "Reset": "", "Reset All Models": "", "Reset Upload Directory": "Återställ uppladdningskatalog", "Reset Vector Storage/Knowledge": "", "Reset view": "", "Response generation stopped": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "", "Response splitting": "", "Result": "", "Retrieval Query Generation": "", "Rich Text Input for Chat": "", "RK": "", "Role": "Roll", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "", "Running": "<PERSON><PERSON><PERSON>", "Save": "Spara", "Save & Create": "Spara och skapa", "Save & Update": "Spara och uppdatera", "Save As Copy": "", "Save Tag": "", "Saved": "", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Att spara chatloggar direkt till din webbläsares lagring stöds inte längre. Ta en stund och ladda ner och radera dina chattloggar genom att klicka på knappen nedan. Oroa dig inte, du kan enkelt importera dina chattloggar till backend genom", "Scroll to bottom when switching between branches": "", "Search": "<PERSON>ö<PERSON>", "Search a model": "<PERSON><PERSON><PERSON> efter en modell", "Search Base": "", "Search Chats": "Sök i chattar", "Search Collection": "", "Search Filters": "", "search for tags": "", "Search Functions": "", "Search Knowledge": "", "Search Models": "<PERSON><PERSON><PERSON> modeller", "Search options": "", "Search Prompts": "Sök instruktioner", "Search Result Count": "<PERSON><PERSON>", "Search the web": "", "Search Tools": "Sökverktyg", "Search users": "", "SearchApi API Key": "", "SearchApi Engine": "", "Searched {{count}} sites": "", "Searching \"{{searchQuery}}\"": "<PERSON><PERSON><PERSON> \"{{searchQuery}}\"", "Searching Knowledge for \"{{searchQuery}}\"": "", "Searxng Query URL": "Searxng Query URL", "See readme.md for instructions": "Se readme.md för instruktioner", "See what's new": "Se vad som är nytt", "Seed": "Seed", "Select a base model": "Välj en basmodell", "Select a engine": "Välj en motor", "Select a function": "", "Select a group": "", "Select a model": "<PERSON><PERSON><PERSON><PERSON> en modell", "Select a pipeline": "Välj en rörledning", "Select a pipeline url": "Välj en URL för rörledningen", "Select a tool": "", "Select Engine": "", "Select Knowledge": "", "Select model": "<PERSON><PERSON><PERSON><PERSON> en modell", "Select only one model to call": "<PERSON><PERSON><PERSON><PERSON> endast en modell att ringa", "Selected model(s) do not support image inputs": "Valda modeller stöder inte bildinmatningar", "Semantic distance to query": "", "Send": "<PERSON><PERSON><PERSON>", "Send a message": "", "Send a Message": "Skicka ett meddelande", "Send message": "<PERSON><PERSON><PERSON> medd<PERSON>", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "", "September": "september", "Serper API Key": "Serper API-nyckel", "Serply API Key": "Serply API-nyckel", "Serpstack API Key": "Serpstack API-nyckel", "Server connection verified": "Serveranslutning verifierad", "Set as default": "Ange som standard", "Set CFG Scale": "", "Set Default Model": "<PERSON><PERSON>", "Set embedding model": "", "Set embedding model (e.g. {{model}})": "<PERSON><PERSON><PERSON> in embedding modell (t.ex. {{model}})", "Set Image Size": "<PERSON><PERSON>", "Set reranking model (e.g. {{model}})": "<PERSON><PERSON><PERSON> in reranking modell (t.ex. {{model}})", "Set Sampler": "", "Set Scheduler": "", "Set Steps": "<PERSON><PERSON> steg", "Set Task Model": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Set the number of GPU devices used for computation. This option controls how many GPU devices (if available) are used to process incoming requests. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "", "Set Voice": "<PERSON><PERSON>", "Set whisper model": "", "Sets how far back for the model to look back to prevent repetition. (Default: 64, 0 = disabled, -1 = num_ctx)": "", "Sets how strongly to penalize repetitions. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. (Default: 1.1)": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt. (Default: random)": "", "Sets the size of the context window used to generate the next token. (Default: 2048)": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "", "Settings": "Inställningar", "Settings saved successfully!": "Inställningar sparades framgångsrikt!", "Share": "Dela", "Share Chat": "<PERSON><PERSON> chatt", "Share to OpenWebUI Community": "Dela till OpenWebUI Community", "Show": "Visa", "Show \"What's New\" modal on login": "", "Show Admin Details in Account Pending Overlay": "Visa administratörsinformation till väntande konton", "Show shortcuts": "Visa genvägar", "Show your support!": "", "Sign in": "Logga in", "Sign in to {{WEBUI_NAME}}": "", "Sign in to {{WEBUI_NAME}} with LDAP": "", "Sign Out": "Logga ut", "Sign up": "Registrera dig", "Sign up to {{WEBUI_NAME}}": "", "Signing in to {{WEBUI_NAME}}": "", "sk-1234": "", "Source": "<PERSON><PERSON><PERSON>", "Speech Playback Speed": "", "Speech recognition error: {{error}}": "Fel vid taligenkänning: {{error}}", "Speech-to-Text Engine": "Tal-till-text-motor", "Stop": "", "Stop Sequence": "Stoppsekvens", "Stream Chat Response": "", "STT Model": "Tal-till-text-modell", "STT Settings": "Tal-till-text-inställningar", "Success": "Framgång", "Successfully updated.": "Uppdaterades framgångsrikt.", "Suggested prompts to get you started": "", "Support": "", "Support this plugin:": "", "Sync directory": "", "System": "System", "System Instructions": "", "System Prompt": "Systeminstruktion", "Tags Generation": "", "Tags Generation Prompt": "", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting. (default: 1)": "", "Tap to interrupt": "", "Tavily API Key": "", "Temperature": "Temperatur", "Template": "Mall", "Temporary Chat": "", "Text Splitter": "", "Text-to-Speech Engine": "Text-till-tal-motor", "Tfs Z": "Tfs Z", "Thanks for your feedback!": "Tack för din feedback!", "The Application Account DN you bind with for search": "", "The base to search for users": "", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.  (Default: 512)": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "", "The LDAP attribute that maps to the username that users use to sign in.": "", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "Betyget ska vara ett värde mellan 0.0 (0%) och 1.0 (100%).", "The temperature of the model. Increasing the temperature will make the model answer more creatively. (Default: 0.8)": "", "Theme": "<PERSON><PERSON>", "Thinking...": "", "This action cannot be undone. Do you wish to continue?": "", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "<PERSON><PERSON> s<PERSON>äller att dina värdefulla samtal sparas säkert till din backend-databas. Tack!", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "Detta är en experimentell funktion som kanske inte fungerar som förväntat och som kan komma att ändras när som helst.", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics. (Default: 24)": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.  (Default: 128)": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "", "This response was generated by \"{{model}}\"": "", "This will delete": "", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "", "This will delete all models including custom models": "", "This will delete all models including custom models and cannot be undone.": "", "This will reset the knowledge base and sync all files. Do you wish to continue?": "", "Tika": "", "Tika Server URL required.": "", "Tiktoken": "", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Tips: Upp<PERSON>ra fler variabler genom att trycka på tabb-tangenten i chattinmatningen efter varje ersättning.", "Title": "Titel", "Title (e.g. Tell me a fun fact)": "Titel (t.ex. Berätta en kuriosa)", "Title Auto-Generation": "Automatisk generering av titel", "Title cannot be an empty string.": "Titeln får inte vara en tom sträng.", "Title Generation Prompt": "Instruktion för tite<PERSON>", "TLS": "", "To access the available model names for downloading,": "<PERSON><PERSON><PERSON> att komma åt de tillgängliga modellnamnen för nedladdning,", "To access the GGUF models available for downloading,": "<PERSON><PERSON><PERSON> att komma åt de GGUF-modellerna som finns tillgängliga för nedladdning,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "<PERSON><PERSON><PERSON> att få tillgång till WebUI, kontakta administratören. Administratörer kan hantera behörigheter från administrationspanelen.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "", "To learn more about available endpoints, visit our documentation.": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "", "To select actions here, add them to the \"Functions\" workspace first.": "", "To select filters here, add them to the \"Functions\" workspace first.": "", "To select toolkits here, add them to the \"Tools\" workspace first.": "Om du vill välja verktygslådor här måste du först lägga till dem i arbetsytan \"Verktyg\".", "Toast notifications for new updates": "", "Today": "<PERSON><PERSON>", "Toggle settings": "Växla inställningar", "Toggle sidebar": "<PERSON><PERSON><PERSON><PERSON>", "Token": "", "Tokens To Keep On Context Refresh (num_keep)": "Tokens att behålla vid kontextuppdatering (num_keep)", "Tool created successfully": "", "Tool deleted successfully": "", "Tool Description": "", "Tool ID": "", "Tool imported successfully": "", "Tool Name": "", "Tool updated successfully": "", "Tools": "Verktyg", "Tools Access": "", "Tools are a function calling system with arbitrary code execution": "", "Tools have a function calling system that allows arbitrary code execution": "", "Tools have a function calling system that allows arbitrary code execution.": "", "Top K": "Topp <PERSON>", "Top P": "Topp P", "Transformers": "", "Trouble accessing Ollama?": "Problem med att komma åt Ollama?", "TTS Model": "Text-till-tal-modell", "TTS Settings": "Text-till-tal-inställningar", "TTS Voice": "Text-till-tal-r<PERSON>st", "Type": "<PERSON><PERSON>", "Type Hugging Face Resolve (Download) URL": "Skriv Hugging Face Resolve (nedladdning) URL", "Uh-oh! There was an issue with the response.": "", "UI": "", "Unarchive All": "", "Unarchive All Archived Chats": "", "Unarchive Chat": "", "Unlock mysteries": "", "Unpin": "", "Unravel secrets": "", "Untagged": "", "Update": "", "Update and Copy Link": "Uppdatera och kopiera länk", "Update for the latest features and improvements.": "", "Update password": "Uppdatera lösenord", "Updated": "", "Updated at": "", "Updated At": "", "Upload": "", "Upload a GGUF model": "Ladda upp en GGUF-modell", "Upload directory": "", "Upload files": "", "Upload Files": "Ladda upp filer", "Upload Pipeline": "Ladda upp rörledning", "Upload Progress": "Uppladdningsframsteg", "URL": "", "URL Mode": "URL-läge", "Use '#' in the prompt input to load and include your knowledge.": "", "Use Gravatar": "<PERSON><PERSON><PERSON><PERSON> Gravatar", "Use groups to group your users and assign permissions.": "", "Use Initials": "<PERSON><PERSON><PERSON><PERSON> <PERSON>er", "use_mlock (Ollama)": "use_mlock (Ollama)", "use_mmap (Ollama)": "use_mmap (Ollama)", "user": "anvä<PERSON><PERSON>", "User": "", "User location successfully retrieved.": "", "Username": "", "Users": "Användare", "Using the default arena model with all models. Click the plus button to add custom models.": "", "Utilize": "<PERSON><PERSON><PERSON><PERSON>", "Valid time units:": "Giltiga tidsenheter:", "Valves": "", "Valves updated": "", "Valves updated successfully": "", "variable": "variabel", "variable to have them replaced with clipboard content.": "variabel för att få dem ersatta med urklippsinnehåll.", "Version": "Version", "Version {{selectedVersion}} of {{totalVersions}}": "", "Very bad": "", "View Replies": "", "Visibility": "", "Voice": "", "Voice Input": "", "Warning": "Varning", "Warning:": "", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Varning: <PERSON><PERSON> du uppdaterar eller ä<PERSON>r din embedding modell måste du importera alla dokument igen.", "Web": "<PERSON>", "Web API": "Webb-API", "Web Loader Settings": "Web Loader-inställningar", "Web Search": "Webbsökning", "Web Search Engine": "Webbsökmotor", "Web Search Query Generation": "", "Webhook URL": "Webhook-URL", "WebUI Settings": "WebUI-inställningar", "WebUI URL": "", "WebUI will make requests to \"{{url}}/api/chat\"": "", "WebUI will make requests to \"{{url}}/chat/completions\"": "", "What are you trying to achieve?": "", "What are you working on?": "", "What didn't you like about this response?": "", "What’s New in": "Vad är nytt i", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "", "wherever you are": "", "Whisper (Local)": "Whisper (lokal)", "Widescreen Mode": "Bredbildsläge", "Won": "", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text. (Default: 0.9)": "", "Workspace": "A<PERSON><PERSON><PERSON><PERSON>", "Workspace Permissions": "", "Write a prompt suggestion (e.g. Who are you?)": "Skriv ett instruktionsförslag (t.ex. Vem är du?)", "Write a summary in 50 words that summarizes [topic or keyword].": "Skriv en sammanfattning på 50 ord som sammanfattar [ämne eller ny<PERSON>].", "Write something...": "", "Write your model template content here": "", "Yesterday": "Igår", "You": "Dig", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "Du kan anpassa dina interaktioner med stora språkmodeller genom att lägga till minnen via knappen '<PERSON><PERSON>' nedan, så att de blir mer användbara och skräddarsydda för dig.", "You cannot upload an empty file.": "", "You have no archived conversations.": "Du har inga arkiverade samtal.", "You have shared this chat": "Du har delat denna chatt", "You're a helpful assistant.": "Du är en hjälpsam assistent.", "Your account status is currently pending activation.": "Ditt konto väntar på att bli aktiverat", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "", "Youtube": "Youtube", "Youtube Loader Settings": "Youtube Loader-inställningar"}