{"-1 for no limit, or a positive integer for a specific limit": "", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' ან '-1' ვადის გასვლისთვის.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "", "(e.g. `sh webui.sh --api`)": "(მაგ. `sh webui.sh --api`)", "(latest)": "(უახლესი)", "{{ models }}": "{{ models }}", "{{COUNT}} Replies": "", "{{user}}'s Chats": "{{user}}-ის ჩათები", "{{webUIName}} Backend Required": "{{webUIName}} საჭიროა ბექენდი", "*Prompt node ID(s) are required for image generation": "", "A new version (v{{LATEST_VERSION}}) is now available.": "", "A task model is used when performing tasks such as generating titles for chats and web search queries": "დავალების მოდელი გამოიყენება ისეთი ამოცანების შესრულებისას, როგორიცაა ჩეთების სათაურების გენერირება და ვებ – ძიების მოთხოვნები", "a user": "მომხმარებელი", "About": "შესახებ", "Access": "", "Access Control": "", "Accessible to all users": "", "Account": "ანგარიში", "Account Activation Pending": "", "Actions": "", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "", "Active Users": "", "Add": "დამატება", "Add a model ID": "", "Add a short description about what this model does": "დაამატეთ მოკლე აღწერა იმის შესახებ, თუ რას აკეთებს ეს მოდელი", "Add a tag": "დაამატე ტეგი", "Add Arena Model": "", "Add Connection": "", "Add Content": "", "Add content here": "", "Add custom prompt": "პირველადი მოთხოვნის დამატება", "Add Files": "ფაილების დამატება", "Add Group": "", "Add Memory": "მემორიის დამატება", "Add Model": "მოდელის დამატება", "Add Reaction": "", "Add Tag": "", "Add Tags": "ტეგების დამატება", "Add text content": "", "Add User": "მომხმარებლის დამატება", "Add User Group": "", "Adjusting these settings will apply changes universally to all users.": "ამ პარამეტრების რეგულირება ცვლილებებს უნივერსალურად გამოიყენებს ყველა მომხმარებლისთვის", "admin": "ადმინისტრატორი", "Admin": "", "Admin Panel": "ადმინ პანელი", "Admin Settings": "ადმინისტრატორის ხელსაწყოები", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "", "Advanced Parameters": "დამატებითი პარამეტრები", "Advanced Params": "მოწინავე პარამები", "All Documents": "ყველა დოკუმენტი", "All models deleted successfully": "", "Allow Chat Delete": "", "Allow Chat Deletion": "მიმოწერის წაშლის დაშვება", "Allow Chat Edit": "", "Allow File Upload": "", "Allow non-local voices": "", "Allow Temporary Chat": "", "Allow User Location": "", "Allow Voice Interruption in Call": "", "Allowed Endpoints": "", "Already have an account?": "უკვე გაქვს ანგარიში?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out. (Default: 0.0)": "", "an assistant": "ასისტენტი", "and": "და", "and {{COUNT}} more": "", "and create a new shared link.": "და შექმენით ახალი გაზიარებული ბმული.", "API Base URL": "API საბაზისო URL", "API Key": "API გასაღები", "API Key created.": "API გასაღები შექმნილია.", "API Key Endpoint Restrictions": "", "API keys": "API გასაღები", "Application DN": "", "Application DN Password": "", "applies to all users with the \"user\" role": "", "April": "აპრილი", "Archive": "არქივი", "Archive All Chats": "არქივი ყველა ჩატი", "Archived Chats": "ჩატის ისტორიის არქივი", "archived-chat-export": "", "Are you sure you want to delete this channel?": "", "Are you sure you want to delete this message?": "", "Are you sure you want to unarchive all archived chats?": "", "Are you sure?": "დარწმუნებული ხარ?", "Arena Models": "", "Artifacts": "", "Ask a question": "", "Assistant": "", "Attach file": "ფაილის ჩაწერა", "Attribute for Username": "", "Audio": "ხმოვანი", "August": "აგვისტო", "Authenticate": "", "Auto-Copy Response to Clipboard": "პასუხის ავტომატური კოპირება ბუფერში", "Auto-playback response": "ავტომატური დაკვრის პასუხი", "Autocomplete Generation": "", "Autocomplete Generation Input Max Length": "", "Automatic1111": "", "AUTOMATIC1111 Api Auth String": "", "AUTOMATIC1111 Base URL": "AUTOMATIC1111 საბაზისო მისამართი", "AUTOMATIC1111 Base URL is required.": "AUTOMATIC1111 საბაზისო მისამართი აუცილებელია", "Available list": "", "available!": "ხელმისაწვდომია!", "Azure AI Speech": "", "Azure Region": "", "Back": "უკან", "Bad": "", "Bad Response": "ხარვეზი", "Banners": "რეკლამა", "Base Model (From)": "საბაზო მოდელი (-დან)", "Batch Size (num_batch)": "", "before": "ადგილზე", "Beta": "", "BETA": "", "Bing Search V7 Endpoint": "", "Bing Search V7 Subscription Key": "", "Brave Search API Key": "Brave Search API გასაღები", "By {{name}}": "", "Bypass SSL verification for Websites": "SSL-ის ვერიფიკაციის გააუქმება ვებსაიტებზე", "Call": "", "Call feature is not supported when using Web STT engine": "", "Camera": "", "Cancel": "გაუქმება", "Capabilities": "შესაძლებლობები", "Capture": "", "Certificate Path": "", "Change Password": "პაროლის შეცვლა", "Channel Name": "", "Channels": "", "Character": "", "Character limit for autocomplete generation input": "", "Chart new frontiers": "", "Chat": "მიმოწერა", "Chat Background Image": "", "Chat Bubble UI": "ჩატის ბულბი", "Chat Controls": "", "Chat direction": "ჩატის მიმართულება", "Chat Overview": "", "Chat Permissions": "", "Chat Tags Auto-Generation": "", "Chats": "მიმოწერები", "Check Again": "თავიდან შემოწმება", "Check for updates": "განახლებების ძიება", "Checking for updates...": "მიმდინარეობს განახლებების ძიება...", "Choose a model before saving...": "აირჩიეთ მოდელი შენახვამდე...", "Chunk Overlap": "გადახურვა ფრაგმენტულია", "Chunk Params": "გადახურვის პარამეტრები", "Chunk Size": "გადახურვის ზომა", "Ciphers": "", "Citation": "ციტატა", "Clear memory": "", "click here": "", "Click here for filter guides.": "", "Click here for help.": "დახმარებისთვის, დააკლიკე აქ", "Click here to": "დააკლიკე აქ", "Click here to download user import template file.": "", "Click here to learn more about faster-whisper and see the available models.": "", "Click here to select": "ასარჩევად, დააკლიკე აქ", "Click here to select a csv file.": "ასარჩევად, დააკლიკე აქ", "Click here to select a py file.": "", "Click here to upload a workflow.json file.": "", "click here.": "დააკლიკე აქ", "Click on the user role button to change a user's role.": "დააკლიკეთ მომხმარებლის როლის ღილაკს რომ შეცვალოთ მომხმარების როლი", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "", "Clone": "კლონი", "Close": "დახურვა", "Code execution": "", "Code formatted successfully": "", "Collection": "ნაკრები", "Color": "", "ComfyUI": "ComfyUI", "ComfyUI API Key": "", "ComfyUI Base URL": "ComfyUI საბაზისო URL", "ComfyUI Base URL is required.": "ComfyUI საბაზისო URL აუცილებელია.", "ComfyUI Workflow": "", "ComfyUI Workflow Nodes": "", "Command": "ბრძანება", "Completions": "", "Concurrent Requests": "თანმხლები მოთხოვნები", "Configure": "", "Configure Models": "", "Confirm": "", "Confirm Password": "პაროლის დამოწმება", "Confirm your action": "", "Confirm your new password": "", "Connections": "კავშირები", "Contact Admin for WebUI Access": "", "Content": "კონტენტი", "Content Extraction": "", "Context Length": "კონტექსტის სიგრძე", "Continue Response": "პასუხის გაგრძელება", "Continue with {{provider}}": "", "Continue with Email": "", "Continue with LDAP": "", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "", "Controls": "", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text. (Default: 5.0)": "", "Copied": "", "Copied shared chat URL to clipboard!": "ყავს ჩათის URL-ი კლიპბორდში!", "Copied to clipboard": "", "Copy": "კოპირება", "Copy last code block": "ბოლო ბლოკის კოპირება", "Copy last response": "ბოლო პასუხის კოპირება", "Copy Link": "კოპირება", "Copy to clipboard": "", "Copying to clipboard was successful!": "კლავიატურაზე კოპირება წარმატებით დასრულდა", "Create": "", "Create a knowledge base": "", "Create a model": "შექმენით მოდელი", "Create Account": "ანგარიშის შექმნა", "Create Admin Account": "", "Create Channel": "", "Create Group": "", "Create Knowledge": "", "Create new key": "პირადი ღირებულბრის შექმნა", "Create new secret key": "პირადი ღირებულბრის შექმნა", "Created at": "შექმნილია", "Created At": "შექმნილია", "Created by": "", "CSV Import": "", "Current Model": "მიმდინარე მოდელი", "Current Password": "მიმდინარე პაროლი", "Custom": "საკუთარი", "Dark": "მუქი", "Database": "მონაცემთა ბაზა", "December": "დეკემბერი", "Default": "დეფოლტი", "Default (Open AI)": "", "Default (SentenceTransformers)": "დეფოლტ (SentenceTransformers)", "Default Model": "ნაგულისხმები მოდელი", "Default model updated": "დეფოლტ მოდელი განახლებულია", "Default Models": "", "Default permissions": "", "Default permissions updated successfully": "", "Default Prompt Suggestions": "დეფოლტ პრომპტი პირველი პირველი", "Default to 389 or 636 if TLS is enabled": "", "Default to ALL": "", "Default User Role": "მომხმარებლის დეფოლტ როლი", "Delete": "წაშლა", "Delete a model": "მოდელის წაშლა", "Delete All Chats": "ყველა ჩატის წაშლა", "Delete All Models": "", "Delete chat": "შეტყობინების წაშლა", "Delete Chat": "შეტყობინების წაშლა", "Delete chat?": "", "Delete folder?": "", "Delete function?": "", "Delete Message": "", "Delete prompt?": "", "delete this link": "ბმულის წაშლა", "Delete tool?": "", "Delete User": "მომხმარებლის წაშლა", "Deleted {{deleteModelTag}}": "{{deleteModelTag}} წაშლილია", "Deleted {{name}}": "Deleted {{name}}", "Deleted User": "", "Describe your knowledge base and objectives": "", "Description": "აღწერა", "Disabled": "", "Discover a function": "", "Discover a model": "გაიგეთ მოდელი", "Discover a prompt": "აღმოაჩინეთ მოთხოვნა", "Discover a tool": "", "Discover wonders": "", "Discover, download, and explore custom functions": "", "Discover, download, and explore custom prompts": "აღმოაჩინეთ, ჩამოტვირთეთ და შეისწავლეთ მორგებული მოთხოვნები", "Discover, download, and explore custom tools": "", "Discover, download, and explore model presets": "აღმოაჩინეთ, ჩამოტვირთეთ და შეისწავლეთ მოდელის წინასწარ პარამეტრები", "Dismissible": "", "Display": "", "Display Emoji in Call": "", "Display the username instead of You in the Chat": "ჩატში აჩვენე მომხმარებლის სახელი თქვენს ნაცვლად", "Displays citations in the response": "", "Dive into knowledge": "", "Do not install functions from sources you do not fully trust.": "", "Do not install tools from sources you do not fully trust.": "", "Document": "დოკუმენტი", "Documentation": "", "Documents": "დოკუმენტები", "does not make any external connections, and your data stays securely on your locally hosted server.": "არ ამყარებს გარე კავშირებს და თქვენი მონაცემები უსაფრთხოდ რჩება თქვენს ადგილობრივ სერვერზე.", "Don't have an account?": "არ გაქვს ანგარიში?", "don't install random functions from sources you don't trust.": "", "don't install random tools from sources you don't trust.": "", "Done": "", "Download": "ჩამოტვირთვა გაუქმებულია", "Download canceled": "ჩამოტვირთვა გაუქმებულია", "Download Database": "გადმოწერე მონაცემთა ბაზა", "Drag and drop a file to upload or select a file to view": "", "Draw": "", "Drop any files here to add to the conversation": "გადაიტანეთ ფაილები აქ, რათა დაამატოთ ისინი მიმოწერაში", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "მაგალითად, '30წ', '10მ'. მოქმედი დროის ერთეულები: 'წ', 'წთ', 'სთ'.", "e.g. A filter to remove profanity from text": "", "e.g. My Filter": "", "e.g. My Tools": "", "e.g. my_filter": "", "e.g. my_tools": "", "e.g. Tools for performing various operations": "", "Edit": "რედაქტირება", "Edit Arena Model": "", "Edit Channel": "", "Edit Connection": "", "Edit Default Permissions": "", "Edit Memory": "", "Edit User": "მომხმარებლის ედიტირება", "Edit User Group": "", "ElevenLabs": "", "Email": "ელ-ფოსტა", "Embark on adventures": "", "Embedding Batch Size": "", "Embedding Model": "ჩასმის ძირითადი პროგრამა", "Embedding Model Engine": "ჩასმის ძირითადი პროგრამა", "Embedding model set to \"{{embedding_model}}\"": "ჩასმის ძირითადი პროგრამა ჩართულია \"{{embedding_model}}\"", "Enable API Key": "", "Enable autocomplete generation for chat messages": "", "Enable Community Sharing": "საზოგადოების გაზიარების ჩართვა", "Enable Google Drive": "", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "", "Enable Message Rating": "", "Enable Mirostat sampling for controlling perplexity. (Default: 0, 0 = Disabled, 1 = Mirostat, 2 = Mirostat 2.0)": "", "Enable New Sign Ups": "ახალი რეგისტრაციების ჩართვა", "Enable Web Search": "ვებ ძიების ჩართვა", "Enabled": "", "Engine": "", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "გთხოვთ, უზრუნველყოთ, რომთქვევის CSV-ფაილი შეიცავს 4 ველი, ჩაწერილი ორივე ველი უდრის პირველი ველით.", "Enter {{role}} message here": "შეიყვანე {{role}} შეტყობინება აქ", "Enter a detail about yourself for your LLMs to recall": "შეიყვანე დეტალი ჩემთათვის, რომ ჩვენი LLMs-ს შეიძლოს აღაქვს", "Enter api auth string (e.g. username:password)": "", "Enter Application DN": "", "Enter Application DN Password": "", "Enter Bing Search V7 Endpoint": "", "Enter Bing Search V7 Subscription Key": "", "Enter Brave Search API Key": "შეიყვანეთ Brave Search API გასაღები", "Enter certificate path": "", "Enter CFG Scale (e.g. 7.0)": "", "Enter Chunk Overlap": "შეიყვანეთ ნაწილის გადახურვა", "Enter Chunk Size": "შეიყვანე ბლოკის ზომა", "Enter description": "", "Enter Github Raw URL": "შეიყვანეთ Github Raw URL", "Enter Google PSE API Key": "შეიყვანეთ Google PSE API გასაღები", "Enter Google PSE Engine Id": "შეიყვანეთ Google PSE ძრავის ID", "Enter Image Size (e.g. 512x512)": "შეიყვანეთ სურათის ზომა (მაგ. 512x512)", "Enter Jina API Key": "", "Enter Kagi Search API Key": "", "Enter language codes": "შეიყვანეთ ენის კოდი", "Enter Model ID": "", "Enter model tag (e.g. {{modelTag}})": "შეიყვანეთ მოდელის ტეგი (მაგ. {{modelTag}})", "Enter Mojeek Search API Key": "", "Enter name or email": "", "Enter Number of Steps (e.g. 50)": "შეიყვანეთ ნაბიჯების რაოდენობა (მაგ. 50)", "Enter proxy URL (e.g. **************************:port)": "", "Enter Sampler (e.g. Euler a)": "", "Enter Scheduler (e.g. Karras)": "", "Enter Score": "შეიყვანეთ ქულა", "Enter SearchApi API Key": "", "Enter SearchApi Engine": "", "Enter Searxng Query URL": "შეიყვანეთ Searxng Query URL", "Enter Seed": "", "Enter Serper API Key": "შეიყვანეთ Serper API Key", "Enter Serply API Key": "", "Enter Serpstack API Key": "შეიყვანეთ Serpstack API Key", "Enter server host": "", "Enter server label": "", "Enter server port": "", "Enter stop sequence": "შეიყვანეთ ტოპ თანმიმდევრობა", "Enter system prompt": "", "Enter Tavily API Key": "", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter Tika Server URL": "", "Enter Top K": "შეიყვანეთ Top K", "Enter URL (e.g. http://127.0.0.1:7860/)": "შეიყვანეთ მისამართი (მაგალითად http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "შეიყვანეთ მისამართი (მაგალითად http://localhost:11434)", "Enter your current password": "", "Enter Your Email": "შეიყვანეთ თქვენი ელ-ფოსტა", "Enter Your Full Name": "შეიყვანეთ თქვენი სრული სახელი", "Enter your message": "", "Enter your new password": "", "Enter Your Password": "შეიყვანეთ თქვენი პაროლი", "Enter your prompt": "", "Enter Your Role": "შეიყვანეთ თქვენი როლი", "Enter Your Username": "", "Enter your webhook URL": "", "Error": "შეცდომა", "ERROR": "", "Error accessing Google Drive: {{error}}": "", "Error uploading file: {{error}}": "", "Evaluations": "", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "", "Example: ALL": "", "Example: ou=users,dc=foo,dc=example": "", "Example: sAMAccountName or uid or userPrincipalName": "", "Exclude": "", "Experimental": "ექსპერიმენტალური", "Explore the cosmos": "", "Export": "ექსპორტი", "Export All Archived Chats": "", "Export All Chats (All Users)": "ექსპორტი ყველა ჩათი (ყველა მომხმარებელი)", "Export chat (.json)": "", "Export Chats": "მიმოწერის ექსპორტირება", "Export Config to JSON File": "", "Export Functions": "", "Export Models": "ექსპორტის მოდელები", "Export Presets": "", "Export Prompts": "მოთხოვნების ექსპორტი", "Export to CSV": "", "Export Tools": "", "External Models": "", "Extremely bad": "", "Failed to add file.": "", "Failed to create API Key.": "API ღილაკის შექმნა ვერ მოხერხდა.", "Failed to read clipboard contents": "ბუფერში შიგთავსის წაკითხვა ვერ მოხერხდა", "Failed to save models configuration": "", "Failed to update settings": "", "February": "თებერვალი", "Feedback History": "", "Feedbacks": "", "File": "", "File added successfully.": "", "File content updated successfully.": "", "File Mode": "ფაილური რეჟიმი", "File not found.": "ფაილი ვერ მოიძებნა", "File removed successfully.": "", "File size should not exceed {{maxSize}} MB.": "", "File uploaded successfully": "", "Files": "", "Filter is now globally disabled": "", "Filter is now globally enabled": "", "Filters": "", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "აღმოჩენილია თითის ანაბეჭდის გაყალბება: ინიციალების გამოყენება ავატარად შეუძლებელია. დეფოლტ პროფილის დეფოლტ სურათი.", "Fluidly stream large external response chunks": "თხევადი ნაკადი დიდი გარე საპასუხო ნაწილაკების", "Focus chat input": "ჩეთის შეყვანის ფოკუსი", "Folder deleted successfully": "", "Folder name cannot be empty": "", "Folder name cannot be empty.": "", "Folder name updated successfully": "", "Forge new paths": "", "Form": "", "Format your variables using brackets like this:": "", "Frequency Penalty": "სიხშირის ჯარიმა", "Function": "", "Function created successfully": "", "Function deleted successfully": "", "Function Description": "", "Function ID": "", "Function is now globally disabled": "", "Function is now globally enabled": "", "Function Name": "", "Function updated successfully": "", "Functions": "", "Functions allow arbitrary code execution": "", "Functions allow arbitrary code execution.": "", "Functions imported successfully": "", "General": "ზოგადი", "General Settings": "ზოგადი პარამეტრები", "Generate Image": "", "Generating search query": "საძიებო მოთხოვნის გენერირება", "Get started": "", "Get started with {{WEBUI_NAME}}": "", "Global": "", "Good Response": "დიდი პასუხი", "Google Drive": "", "Google PSE API Key": "Google PSE API გასაღები", "Google PSE Engine Id": "Google PSE ძრავის Id", "Group created successfully": "", "Group deleted successfully": "", "Group Description": "", "Group Name": "", "Group updated successfully": "", "Groups": "", "GSA Chat can make mistakes. Review all responses for accuracy.": "", "h:mm a": "h:mm a", "Haptic Feedback": "", "Harmful or offensive": "", "has no conversations.": "არა უფლება ჩაწერა", "Hello, {{name}}": "გამარჯობა, {{name}}", "Help": "დახმარება", "Help us create the best community leaderboard by sharing your feedback history!": "", "Hex Color": "", "Hex Color - Leave empty for default color": "", "Hide": "დამალვა", "Host": "", "How can I help you today?": "როგორ შემიძლია დაგეხმარო დღეს?", "How would you rate this response?": "", "Hybrid Search": "ჰიბრიდური ძებნა", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "", "ID": "", "Ignite curiosity": "", "Image Compression": "", "Image Generation (Experimental)": "სურათების გენერაცია (ექსპერიმენტული)", "Image Generation Engine": "სურათის გენერაციის ძრავა", "Image Max Compression Size": "", "Image Settings": "სურათის პარამეტრები", "Images": "სურათები", "Import Chats": "მიმოწერების იმპორტი", "Import Config from JSON File": "", "Import Functions": "", "Import Models": "იმპორტის მოდელები", "Import Presets": "", "Import Prompts": "მოთხოვნების იმპორტი", "Import Tools": "", "Include": "", "Include `--api-auth` flag when running stable-diffusion-webui": "", "Include `--api` flag when running stable-diffusion-webui": "ჩართეთ `--api` დროშა stable-diffusion-webui-ის გაშვებისას", "Incomplete response": "", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive. (Default: 0.1)": "", "Info": "ინფორმაცია", "Input commands": "შეყვანით ბრძანებებს", "Install from Github URL": "დააინსტალირეთ Github URL- დან", "Instant Auto-Send After Voice Transcription": "", "Interface": "ინტერფეისი", "Invalid file format.": "", "Invalid Tag": "არასწორი ტეგი", "is typing...": "", "January": "იანვარი", "Jina API Key": "", "join our Discord for help.": "შეუერთდით ჩვენს Discord-ს დახმარებისთვის", "JSON": "JSON", "JSON Preview": "JSON გადახედვა", "July": "ივნისი", "June": "ივლა", "JWT Expiration": "JWT-ის ვადა", "JWT Token": "JWT ტოკენი", "Kagi Search API Key": "", "Keep Alive": "აქტიურად დატოვება", "Key": "", "Keyboard shortcuts": "კლავიატურის მალსახმობები", "Knowledge": "", "Knowledge Access": "", "Knowledge created successfully.": "", "Knowledge deleted successfully.": "", "Knowledge reset successfully.": "", "Knowledge updated successfully": "", "Label": "", "Landing Page Mode": "", "Language": "ენა", "Last Active": "ბოლო აქტიური", "Last Modified": "", "Last reply": "", "Latest users": "", "LDAP": "", "LDAP server updated": "", "Leaderboard": "", "Leave empty for unlimited": "", "Leave empty to include all models from \"{{URL}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{URL}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "", "Leave empty to use the default prompt, or enter a custom prompt": "", "Light": "მსუბუქი", "Listening...": "", "Local": "", "Local Models": "", "Lost": "", "LTR": "LTR", "Made by OpenWebUI Community": "დამზადებულია OpenWebUI საზოგადოების მიერ", "Make sure to enclose them with": "დარწმუნდით, რომ დაურთეთ ისინი", "Make sure to export a workflow.json file as API format from ComfyUI.": "", "Manage": "", "Manage Arena Models": "", "Manage Ollama": "", "Manage Ollama API Connections": "", "Manage OpenAI API Connections": "", "Manage Pipelines": "მილსადენების მართვა", "March": "მარტივი", "Max Tokens (num_predict)": "მაქს ტოკენსი (num_predict)", "Max Upload Count": "", "Max Upload Size": "", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "მაქსიმუმ 3 მოდელის ჩამოტვირთვა შესაძლებელია ერთდროულად. Გთხოვთ სცადოთ მოგვიანებით.", "May": "მაი", "Memories accessible by LLMs will be shown here.": "ლლმ-ს აქვს ხელმისაწვდომი მემორიები აქ იქნება.", "Memory": "მემორია", "Memory added successfully": "", "Memory cleared successfully": "", "Memory deleted successfully": "", "Memory updated successfully": "", "Merge Responses": "", "Message rating should be enabled to use this feature": "", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "შეტყობინებები, რომელსაც თქვენ აგზავნით თქვენი ბმულის შექმნის შემდეგ, არ იქნება გაზიარებული. URL– ის მქონე მომხმარებლებს შეეძლებათ ნახონ საერთო ჩატი.", "Min P": "", "Minimum Score": "მინიმალური ქულა", "Mirostat": "მიროსტატი", "Mirostat Eta": "მიროსტატი ეტა", "Mirostat Tau": "მიროსტატი ტაუ", "MMMM DD, YYYY": "თვე დღე, წელი", "MMMM DD, YYYY HH:mm": "თვე დღე, წელი HH:mm", "MMMM DD, YYYY hh:mm:ss A": "", "Model": "", "Model '{{modelName}}' has been successfully downloaded.": "მოდელი „{{modelName}}“ წარმატებით ჩამოიტვირთა.", "Model '{{modelTag}}' is already in queue for downloading.": "მოდელი „{{modelTag}}“ უკვე ჩამოტვირთვის რიგშია.", "Model {{modelId}} not found": "მოდელი {{modelId}} ვერ მოიძებნა", "Model {{modelName}} is not vision capable": "Model {{modelName}} is not vision capable", "Model {{name}} is now {{status}}": "Model {{name}} is now {{status}}", "Model accepts image inputs": "", "Model created successfully!": "", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "აღმოჩენილია მოდელის ფაილური სისტემის გზა. განახლებისთვის საჭიროა მოდელის მოკლე სახელი, გაგრძელება შეუძლებელია.", "Model Filtering": "", "Model ID": "მოდელის ID", "Model IDs": "", "Model Name": "", "Model not selected": "მოდელი არ არის არჩეული", "Model Params": "მოდელის პარამები", "Model Permissions": "", "Model updated successfully": "", "Modelfile Content": "მოდელური ფაილის კონტენტი", "Models": "მოდელები", "Models Access": "", "Models configuration saved successfully": "", "Mojeek Search API Key": "", "more": "", "More": "ვრცლად", "Name": "სახელი", "Name your knowledge base": "", "New Chat": "ახალი მიმოწერა", "New folder": "", "New Password": "ახალი პაროლი", "new-channel": "", "No content found": "", "No content to speak": "", "No distance available": "", "No feedbacks found": "", "No file selected": "", "No files found.": "", "No groups with access, add a group to grant access": "", "No HTML, CSS, or JavaScript content found.": "", "No knowledge found": "", "No model IDs": "", "No models found": "", "No models selected": "", "No results found": "ჩვენ ვერ პოულობით ნაპოვნი ჩაწერები", "No search query generated": "ძიების მოთხოვნა არ არის გენერირებული", "No source available": "წყარო არ არის ხელმისაწვდომი", "No users were found.": "", "No valves to update": "", "None": "არცერთი", "Not accurate": "", "Not relevant": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "შენიშვნა: თუ თქვენ დააყენებთ მინიმალურ ქულას, ძებნა დააბრუნებს მხოლოდ დოკუმენტებს მინიმალური ქულის მეტი ან ტოლი ქულით.", "Notes": "", "Notification Sound": "", "Notification Webhook": "", "Notifications": "შეტყობინება", "November": "ნოემბერი", "num_gpu (Ollama)": "", "num_thread (Ollama)": "num_thread (ოლამა)", "OAuth ID": "", "October": "ოქტომბერი", "Off": "გამორთვა", "Okay, Let's Go!": "კარგი, წავედით!", "OLED Dark": "OLED მუქი", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API disabled": "Ollama API გამორთულია", "Ollama API settings updated": "", "Ollama Version": "Ollama ვერსია", "On": "ჩართვა", "Only alphanumeric characters and hyphens are allowed": "", "Only alphanumeric characters and hyphens are allowed in the command string.": "ბრძანების სტრიქონში დაშვებულია მხოლოდ ალფანუმერული სიმბოლოები და დეფისები.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "", "Only select users and groups with permission can access": "", "Oops! Looks like the URL is invalid. Please double-check and try again.": "უი! როგორც ჩანს, მისამართი არასწორია. გთხოვთ, გადაამოწმოთ და ისევ სცადოთ.", "Oops! There are files still uploading. Please wait for the upload to complete.": "", "Oops! There was an error in the previous response.": "", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "უპს! თქვენ იყენებთ მხარდაუჭერელ მეთოდს (მხოლოდ frontend). გთხოვთ, მოემსახუროთ WebUI-ს ბექენდიდან", "Open in full screen": "", "Open new chat": "ახალი მიმოწერის გახსნა", "Open WebUI uses faster-whisper internally.": "", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "", "OpenAI": "OpenAI", "OpenAI API": "OpenAI API", "OpenAI API Config": "OpenAI API პარამეტრები", "OpenAI API Key is required.": "OpenAI API გასაღები აუცილებელია", "OpenAI API settings updated": "", "OpenAI URL/Key required.": "OpenAI URL/Key აუცილებელია", "or": "ან", "Organize your users": "", "OUTPUT": "", "Output format": "", "Overview": "", "page": "", "Password": "პაროლი", "Paste Large Text as File": "", "PDF document (.pdf)": "PDF დოკუმენტი (.pdf)", "PDF Extract Images (OCR)": "PDF იდან ამოღებული სურათები (OCR)", "pending": "ლოდინის რეჟიმშია", "Permission denied when accessing media devices": "", "Permission denied when accessing microphone": "", "Permission denied when accessing microphone: {{error}}": "ნებართვა უარყოფილია მიკროფონზე წვდომისას: {{error}}", "Permissions": "", "Personalization": "პერსონალიზაცია", "Pin": "", "Pinned": "", "Pioneer insights": "", "Pipeline deleted successfully": "", "Pipeline downloaded successfully": "", "Pipelines": "მილსადენები", "Pipelines Not Detected": "", "Pipelines Valves": "მილსადენების სარქველები", "Plain text (.txt)": "ტექსტი (.txt)", "Playground": "სათამაშო მოედანი", "Please carefully review the following warnings:": "", "Please enter a prompt": "", "Please fill in all fields.": "", "Please select a model first.": "", "Port": "", "Prefix ID": "", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "", "Previous 30 days": "უკან 30 დღე", "Previous 7 days": "უკან 7 დღე", "Profile Image": "პროფილის სურათი", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Prompt (მაგ. მითხარი სახალისო ფაქტი რომის იმპერიის შესახებ)", "Prompt Content": "მოთხოვნის შინაარსი", "Prompt created successfully": "", "Prompt suggestions": "მოთხოვნის რჩევები", "Prompt updated successfully": "", "Prompts": "მოთხოვნები", "Prompts Access": "", "Provide any specific details": "", "Proxy URL": "", "Pull \"{{searchValue}}\" from Ollama.com": "ჩაიამოვეთ \"{{searchValue}}\" Ollama.com-იდან", "Pull a model from Ollama.com": "Ollama.com იდან მოდელის გადაწერა ", "Query Generation Prompt": "", "Query Params": "პარამეტრების ძიება", "RAG Template": "RAG შაბლონი", "Rating": "", "Re-rank models by topic similarity": "", "Read Aloud": "ხმის ჩაწერა", "Record voice": "ხმის ჩაწერა", "Redirecting you to OpenWebUI Community": "გადამისამართდებით OpenWebUI საზოგადოებაში", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative. (Default: 40)": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "", "References from": "", "Refresh Token Expiration": "", "Regenerate": "ხელახლა გენერირება", "Release Notes": "Გამოშვების შენიშვნები", "Relevance": "", "Remove": "პოპულარობის რაოდენობა", "Remove Model": "პოპულარობის რაოდენობა", "Rename": "პოპულარობის რაოდენობა", "Reorder Models": "", "Repeat Last N": "გაიმეორეთ ბოლო N", "Reply in Thread": "", "Request Mode": "მოთხოვნის რეჟიმი", "Reranking Model": "რექვექტირება", "Reranking model disabled": "რექვექტირება არაა ჩართული", "Reranking model set to \"{{reranking_model}}\"": "Reranking model set to \"{{reranking_model}}\"", "Reset": "", "Reset All Models": "", "Reset Upload Directory": "", "Reset Vector Storage/Knowledge": "", "Reset view": "", "Response generation stopped": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "", "Response splitting": "", "Result": "", "Retrieval Query Generation": "", "Rich Text Input for Chat": "", "RK": "", "Role": "როლი", "Rosé Pine": "ვარდისფერი ფიჭვის ხე", "Rosé Pine Dawn": "ვარდისფერი ფიჭვის გარიჟრაჟი", "RTL": "RTL", "Run": "", "Running": "", "Save": "შენახვა", "Save & Create": "დამახსოვრება და შექმნა", "Save & Update": "დამახსოვრება და განახლება", "Save As Copy": "", "Save Tag": "", "Saved": "", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "ჩეთის ისტორიის შენახვა პირდაპირ თქვენი ბრაუზერის საცავში აღარ არის მხარდაჭერილი. გთხოვთ, დაუთმოთ და წაშალოთ თქვენი ჩატის ჟურნალები ქვემოთ მოცემულ ღილაკზე დაწკაპუნებით. არ ინერვიულოთ, თქვენ შეგიძლიათ მარტივად ხელახლა შემოიტანოთ თქვენი ჩეთის ისტორია ბექენდში", "Scroll to bottom when switching between branches": "", "Search": "ძიება", "Search a model": "მოდელის ძიება", "Search Base": "", "Search Chats": "ჩატების ძებნა", "Search Collection": "", "Search Filters": "", "search for tags": "", "Search Functions": "", "Search Knowledge": "", "Search Models": "საძიებო მოდელები", "Search options": "", "Search Prompts": "მოთხოვნების ძიება", "Search Result Count": "ძიების შედეგების რაოდენობა", "Search the web": "", "Search Tools": "", "Search users": "", "SearchApi API Key": "", "SearchApi Engine": "", "Searched {{count}} sites": "", "Searching \"{{searchQuery}}\"": "", "Searching Knowledge for \"{{searchQuery}}\"": "", "Searxng Query URL": "Searxng Query URL", "See readme.md for instructions": "იხილეთ readme.md ინსტრუქციებისთვის", "See what's new": "სიახლეების ნახვა", "Seed": "სიდი", "Select a base model": "აირჩიეთ ბაზის მოდელი", "Select a engine": "", "Select a function": "", "Select a group": "", "Select a model": "მოდელის არჩევა", "Select a pipeline": "აირჩიეთ მილსადენი", "Select a pipeline url": "აირჩიეთ მილსადენის url", "Select a tool": "", "Select Engine": "", "Select Knowledge": "", "Select model": "მოდელის არჩევა", "Select only one model to call": "", "Selected model(s) do not support image inputs": "შერჩეული მოდელი (ებ) ი არ უჭერს მხარს გამოსახულების შეყვანას", "Semantic distance to query": "", "Send": "გაგზავნა", "Send a message": "", "Send a Message": "შეტყობინების გაგზავნა", "Send message": "შეტყობინების გაგზავნა", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "", "September": "სექტემბერი", "Serper API Key": "Serper API Key", "Serply API Key": "", "Serpstack API Key": "Serpstack API Key", "Server connection verified": "სერვერთან კავშირი დადასტურებულია", "Set as default": "დეფოლტად დაყენება", "Set CFG Scale": "", "Set Default Model": "დეფოლტ მოდელის დაყენება", "Set embedding model": "", "Set embedding model (e.g. {{model}})": "ჩვენება მოდელის დაყენება (მაგ. {{model}})", "Set Image Size": "სურათის ზომის დაყენება", "Set reranking model (e.g. {{model}})": "რეტარირება მოდელის დაყენება (მაგ. {{model}})", "Set Sampler": "", "Set Scheduler": "", "Set Steps": "ნაბიჯების დაყენება", "Set Task Model": "დააყენეთ სამუშაო მოდელი", "Set the number of GPU devices used for computation. This option controls how many GPU devices (if available) are used to process incoming requests. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "", "Set Voice": "ხმის დაყენება", "Set whisper model": "", "Sets how far back for the model to look back to prevent repetition. (Default: 64, 0 = disabled, -1 = num_ctx)": "", "Sets how strongly to penalize repetitions. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. (Default: 1.1)": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt. (Default: random)": "", "Sets the size of the context window used to generate the next token. (Default: 2048)": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "", "Settings": "ხელსაწყოები", "Settings saved successfully!": "პარამეტრები წარმატებით განახლდა!", "Share": "გაზიარება", "Share Chat": "გაზიარება", "Share to OpenWebUI Community": "გააზიარე OpenWebUI საზოგადოებაში ", "Show": "ჩვენება", "Show \"What's New\" modal on login": "", "Show Admin Details in Account Pending Overlay": "", "Show shortcuts": "მალსახმობების ჩვენება", "Show your support!": "", "Sign in": "ავტორიზაცია", "Sign in to {{WEBUI_NAME}}": "", "Sign in to {{WEBUI_NAME}} with LDAP": "", "Sign Out": "გასვლა", "Sign up": "რეგისტრაცია", "Sign up to {{WEBUI_NAME}}": "", "Signing in to {{WEBUI_NAME}}": "", "sk-1234": "", "Source": "წყარო", "Speech Playback Speed": "", "Speech recognition error: {{error}}": "მეტყველების ამოცნობის შეცდომა: {{error}}", "Speech-to-Text Engine": "ხმოვან-ტექსტური ძრავი", "Stop": "", "Stop Sequence": "შეჩერების თანმიმდევრობა", "Stream Chat Response": "", "STT Model": "", "STT Settings": "მეტყველების ამოცნობის პარამეტრები", "Success": "წარმატება", "Successfully updated.": "წარმატებით განახლდა", "Suggested prompts to get you started": "", "Support": "", "Support this plugin:": "", "Sync directory": "", "System": "სისტემა", "System Instructions": "", "System Prompt": "სისტემური მოთხოვნა", "Tags Generation": "", "Tags Generation Prompt": "", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting. (default: 1)": "", "Tap to interrupt": "", "Tavily API Key": "", "Temperature": "ტემპერატურა", "Template": "შაბლონი", "Temporary Chat": "", "Text Splitter": "", "Text-to-Speech Engine": "ტექსტურ-ხმოვანი ძრავი", "Tfs Z": "Tfs Z", "Thanks for your feedback!": "მადლობა გამოხმაურებისთვის!", "The Application Account DN you bind with for search": "", "The base to search for users": "", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.  (Default: 512)": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "", "The LDAP attribute that maps to the username that users use to sign in.": "", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "ქულა 0.0 (0%) და 1.0 (100%) ჩაშენებული უნდა იყოს.", "The temperature of the model. Increasing the temperature will make the model answer more creatively. (Default: 0.8)": "", "Theme": "თემა", "Thinking...": "", "This action cannot be undone. Do you wish to continue?": "", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "ეს უზრუნველყოფს, რომ თქვენი ძვირფასი საუბრები უსაფრთხოდ შეინახება თქვენს backend მონაცემთა ბაზაში. Გმადლობთ!", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics. (Default: 24)": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.  (Default: 128)": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "", "This response was generated by \"{{model}}\"": "", "This will delete": "", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "", "This will delete all models including custom models": "", "This will delete all models including custom models and cannot be undone.": "", "This will reset the knowledge base and sync all files. Do you wish to continue?": "", "Tika": "", "Tika Server URL required.": "", "Tiktoken": "", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "რჩევა: განაახლეთ რამდენიმე ცვლადი სლოტი თანმიმდევრულად, ყოველი ჩანაცვლების შემდეგ ჩატის ღილაკზე დაჭერით.", "Title": "სათაური", "Title (e.g. Tell me a fun fact)": "სათაური (მაგ. გაიხსნე რაღაც ხარისხი)", "Title Auto-Generation": "სათაურის ავტო-გენერაცია", "Title cannot be an empty string.": "სათაური ცარიელი ველი ვერ უნდა იყოს.", "Title Generation Prompt": "სათაურის გენერაციის მოთხოვნა ", "TLS": "", "To access the available model names for downloading,": "ჩამოტვირთვისთვის ხელმისაწვდომი მოდელების სახელებზე წვდომისთვის", "To access the GGUF models available for downloading,": "ჩასატვირთად ხელმისაწვდომი GGUF მოდელებზე წვდომისთვის", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "", "To learn more about available endpoints, visit our documentation.": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "", "To select actions here, add them to the \"Functions\" workspace first.": "", "To select filters here, add them to the \"Functions\" workspace first.": "", "To select toolkits here, add them to the \"Tools\" workspace first.": "", "Toast notifications for new updates": "", "Today": "დღეს", "Toggle settings": "პარამეტრების გადართვა", "Toggle sidebar": "გვერდითი ზოლის გადართვა", "Token": "", "Tokens To Keep On Context Refresh (num_keep)": "", "Tool created successfully": "", "Tool deleted successfully": "", "Tool Description": "", "Tool ID": "", "Tool imported successfully": "", "Tool Name": "", "Tool updated successfully": "", "Tools": "", "Tools Access": "", "Tools are a function calling system with arbitrary code execution": "", "Tools have a function calling system that allows arbitrary code execution": "", "Tools have a function calling system that allows arbitrary code execution.": "", "Top K": "ტოპ K", "Top P": "ტოპ P", "Transformers": "", "Trouble accessing Ollama?": "Ollama-ს ვერ უკავშირდები?", "TTS Model": "", "TTS Settings": "TTS პარამეტრები", "TTS Voice": "", "Type": "ტიპი", "Type Hugging Face Resolve (Download) URL": "სცადე გადმოწერო Hugging Face Resolve URL", "Uh-oh! There was an issue with the response.": "", "UI": "", "Unarchive All": "", "Unarchive All Archived Chats": "", "Unarchive Chat": "", "Unlock mysteries": "", "Unpin": "", "Unravel secrets": "", "Untagged": "", "Update": "", "Update and Copy Link": "განახლება და ბმულის კოპირება", "Update for the latest features and improvements.": "", "Update password": "პაროლის განახლება", "Updated": "", "Updated at": "", "Updated At": "", "Upload": "", "Upload a GGUF model": "GGUF მოდელის ატვირთვა", "Upload directory": "", "Upload files": "", "Upload Files": "ატვირთეთ ფაილები", "Upload Pipeline": "", "Upload Progress": "პროგრესის ატვირთვა", "URL": "", "URL Mode": "URL რეჟიმი", "Use '#' in the prompt input to load and include your knowledge.": "", "Use Gravatar": "გამოიყენე Gravatar", "Use groups to group your users and assign permissions.": "", "Use Initials": "გამოიყენე ინიციალები", "use_mlock (Ollama)": "use_mlock (ოლამა)", "use_mmap (Ollama)": "use_mmap (ოლამა)", "user": "მომხმარებელი", "User": "", "User location successfully retrieved.": "", "Username": "", "Users": "მომხმარებლები", "Using the default arena model with all models. Click the plus button to add custom models.": "", "Utilize": "გამოყენება", "Valid time units:": "მოქმედი დროის ერთეულები", "Valves": "", "Valves updated": "", "Valves updated successfully": "", "variable": "ცვლადი", "variable to have them replaced with clipboard content.": "ცვლადი, რომ შეცვალოს ისინი ბუფერში შიგთავსით.", "Version": "ვერსია", "Version {{selectedVersion}} of {{totalVersions}}": "", "Very bad": "", "View Replies": "", "Visibility": "", "Voice": "", "Voice Input": "", "Warning": "გაფრთხილება", "Warning:": "", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "გაფრთხილება: თუ განაახლებთ ან შეცვლით ჩანერგვის მოდელს, მოგიწევთ ყველა დოკუმენტის ხელახლა იმპორტი.", "Web": "ვები", "Web API": "", "Web Loader Settings": "ვების ჩატარების პარამეტრები", "Web Search": "ვებ ძებნა", "Web Search Engine": "ვებ საძიებო სისტემა", "Web Search Query Generation": "", "Webhook URL": "Webhook URL", "WebUI Settings": "WebUI პარამეტრები", "WebUI URL": "", "WebUI will make requests to \"{{url}}/api/chat\"": "", "WebUI will make requests to \"{{url}}/chat/completions\"": "", "What are you trying to achieve?": "", "What are you working on?": "", "What didn't you like about this response?": "", "What’s New in": "რა არის ახალი", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "", "wherever you are": "", "Whisper (Local)": "", "Widescreen Mode": "", "Won": "", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text. (Default: 0.9)": "", "Workspace": "ვულერი", "Workspace Permissions": "", "Write a prompt suggestion (e.g. Who are you?)": "დაწერეთ მოკლე წინადადება (მაგ. ვინ ხარ?", "Write a summary in 50 words that summarizes [topic or keyword].": "დაწერეთ რეზიუმე 50 სიტყვით, რომელიც აჯამებს [თემას ან საკვანძო სიტყვას].", "Write something...": "", "Write your model template content here": "", "Yesterday": "აღდგენა", "You": "ჩემი", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "", "You cannot upload an empty file.": "", "You have no archived conversations.": "არ ხართ არქივირებული განხილვები.", "You have shared this chat": "ამ ჩატის გააგზავნა", "You're a helpful assistant.": "თქვენ სასარგებლო ასისტენტი ხართ.", "Your account status is currently pending activation.": "", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "", "Youtube": "Youtube", "Youtube Loader Settings": "Youtube Loader Settings"}