{"-1 for no limit, or a positive integer for a specific limit": "", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": " 's', 'm', 'h', 'd', 'w' ou '-1' pour une durée illimitée.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(par ex. `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(par exemple `sh webui.sh --api`)", "(latest)": "(dernier)", "{{ models }}": "{{ modèles }}", "{{COUNT}} Replies": "", "{{user}}'s Chats": "Discussions de {{user}}", "{{webUIName}} Backend Required": "Backend {{webUIName}} requis", "*Prompt node ID(s) are required for image generation": "", "A new version (v{{LATEST_VERSION}}) is now available.": "", "A task model is used when performing tasks such as generating titles for chats and web search queries": "Un modèle de tâche est utilisé lors de l’exécution de tâches telles que la génération de titres pour les conversations et les requêtes de recherche sur le web.", "a user": "un utilisateur", "About": "À propos", "Access": "", "Access Control": "", "Accessible to all users": "", "Account": "<PERSON><PERSON><PERSON>", "Account Activation Pending": "Activation du compte en attente", "Actions": "", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "", "Active Users": "Utilisateurs actifs", "Add": "Ajouter", "Add a model ID": "", "Add a short description about what this model does": "Ajoutez une brève description de ce que fait ce modèle.", "Add a tag": "Ajouter une balise", "Add Arena Model": "", "Add Connection": "", "Add Content": "", "Add content here": "", "Add custom prompt": "Ajouter une prompt personnalisée", "Add Files": "Ajouter des fichiers", "Add Group": "", "Add Memory": "<PERSON><PERSON><PERSON> de la mémoire", "Add Model": "Ajouter un modèle", "Add Reaction": "", "Add Tag": "", "Add Tags": "Ajouter des balises", "Add text content": "", "Add User": "Ajouter un Utilisateur", "Add User Group": "", "Adjusting these settings will apply changes universally to all users.": "L'ajustement de ces paramètres appliquera universellement les changements à tous les utilisateurs.", "admin": "administrateur", "Admin": "Administrateur", "Admin Panel": "Tableau de bord administrateur", "Admin Settings": "Paramètres d'administration", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "Les administrateurs ont accès à tous les outils en tout temps ; les utilisateurs ont besoin d'outils affectés par modèle dans l'espace de travail.", "Advanced Parameters": "Paramètres avancés", "Advanced Params": "Paramètres avancés", "All Documents": "Tous les documents", "All models deleted successfully": "", "Allow Chat Delete": "", "Allow Chat Deletion": "Autoriser la suppression de l'historique de chat", "Allow Chat Edit": "", "Allow File Upload": "", "Allow non-local voices": "Autoriser les voix non locales", "Allow Temporary Chat": "", "Allow User Location": "Autoriser l'emplacement de l'utilisateur", "Allow Voice Interruption in Call": "Autoriser l'interruption vocale pendant un appel", "Allowed Endpoints": "", "Already have an account?": "<PERSON><PERSON>-vous déjà un compte ?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out. (Default: 0.0)": "", "an assistant": "un assistant", "and": "et", "and {{COUNT}} more": "", "and create a new shared link.": "et créer un nouveau lien partagé.", "API Base URL": "URL de base de l'API", "API Key": "Clé d'API", "API Key created.": "Clé d'API générée.", "API Key Endpoint Restrictions": "", "API keys": "Clés d'API", "Application DN": "", "Application DN Password": "", "applies to all users with the \"user\" role": "", "April": "Avril", "Archive": "Archivage", "Archive All Chats": "Archiver toutes les conversations", "Archived Chats": "Conversations archivées", "archived-chat-export": "", "Are you sure you want to delete this channel?": "", "Are you sure you want to delete this message?": "", "Are you sure you want to unarchive all archived chats?": "", "Are you sure?": "Êtes-vous certain ?", "Arena Models": "", "Artifacts": "", "Ask a question": "", "Assistant": "", "Attach file": "Joindre un document", "Attribute for Username": "", "Audio": "Audio", "August": "Août", "Authenticate": "", "Auto-Copy Response to Clipboard": "Copie automatique de la réponse vers le presse-papiers", "Auto-playback response": "Réponse de lecture automatique", "Autocomplete Generation": "", "Autocomplete Generation Input Max Length": "", "Automatic1111": "", "AUTOMATIC1111 Api Auth String": "AUTOMATIC1111 Chaîne d'authentification de l'API", "AUTOMATIC1111 Base URL": "URL de base AUTOMATIC1111", "AUTOMATIC1111 Base URL is required.": "L'URL de base {AUTOMATIC1111} est requise.", "Available list": "", "available!": "disponible !", "Azure AI Speech": "", "Azure Region": "", "Back": "Retour en arrière", "Bad": "", "Bad Response": "Mauvaise réponse", "Banners": "Banniers", "Base Model (From)": "Mod<PERSON>le de base (à partir de)", "Batch Size (num_batch)": "<PERSON><PERSON> du <PERSON> (num_batch)", "before": "avant", "Beta": "", "BETA": "", "Bing Search V7 Endpoint": "", "Bing Search V7 Subscription Key": "", "Brave Search API Key": "Clé API Brave Search", "By {{name}}": "", "Bypass SSL verification for Websites": "Bypasser la vérification SSL pour les sites web", "Call": "<PERSON><PERSON><PERSON>", "Call feature is not supported when using Web STT engine": "La fonction d'appel n'est pas prise en charge lors de l'utilisation du moteur Web STT", "Camera": "Appareil photo", "Cancel": "Annuler", "Capabilities": "Capacités", "Capture": "", "Certificate Path": "", "Change Password": "Changer le mot de passe", "Channel Name": "", "Channels": "", "Character": "", "Character limit for autocomplete generation input": "", "Chart new frontiers": "", "Chat": "Cha<PERSON>", "Chat Background Image": "Image d'arrière-plan de la fenêtre de chat", "Chat Bubble UI": "Bulles de discussion", "Chat Controls": "", "Chat direction": "Direction du chat", "Chat Overview": "", "Chat Permissions": "", "Chat Tags Auto-Generation": "", "Chats": "Conversations", "Check Again": "Vérifiez à nouveau.", "Check for updates": "Vérifier les mises à jour disponibles", "Checking for updates...": "Recherche de mises à jour...", "Choose a model before saving...": "Choisissez un modèle avant de sauvegarder...", "Chunk Overlap": "Chevauchement de blocs", "Chunk Params": "Paramètres d'encombrement", "Chunk Size": "<PERSON><PERSON>loc", "Ciphers": "", "Citation": "Citation", "Clear memory": "Libérer la mémoire", "click here": "", "Click here for filter guides.": "", "Click here for help.": "Cliquez ici pour obtenir de l'aide.", "Click here to": "Cliquez ici pour", "Click here to download user import template file.": "Cliquez ici pour télécharger le fichier modèle d'importation utilisateur.", "Click here to learn more about faster-whisper and see the available models.": "", "Click here to select": "Cliquez ici pour sélectionner", "Click here to select a csv file.": "Cliquez ici pour sélectionner un fichier CSV.", "Click here to select a py file.": "Cliquez ici pour sélectionner un fichier .py.", "Click here to upload a workflow.json file.": "", "click here.": "cliquez ici.", "Click on the user role button to change a user's role.": "Cliquez sur le bouton de rôle d'utilisateur pour modifier le rôle d'un utilisateur.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "L'autorisation d'écriture du presse-papier a été refusée. Veuillez vérifier les paramètres de votre navigateur pour accorder l'accès nécessaire.", "Clone": "Copie conforme", "Close": "<PERSON><PERSON><PERSON>", "Code execution": "", "Code formatted successfully": "Le code a été formaté avec succès", "Collection": "Collection", "Color": "", "ComfyUI": "ComfyUI", "ComfyUI API Key": "", "ComfyUI Base URL": "URL de base ComfyUI", "ComfyUI Base URL is required.": "L'URL de base ComfyUI est requise.", "ComfyUI Workflow": "", "ComfyUI Workflow Nodes": "", "Command": "Commande", "Completions": "", "Concurrent Requests": "Demandes concurrentes", "Configure": "", "Configure Models": "", "Confirm": "Confirmer", "Confirm Password": "Confirmer le mot de passe", "Confirm your action": "Confirmez votre action", "Confirm your new password": "", "Connections": "Connexions", "Contact Admin for WebUI Access": "Contacter l'administrateur pour l'accès à l'interface Web", "Content": "Contenu", "Content Extraction": "", "Context Length": "<PERSON><PERSON><PERSON> du contexte", "Continue Response": "Continuer la réponse", "Continue with {{provider}}": "Continuer avec {{provider}}", "Continue with Email": "", "Continue with LDAP": "", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "Contrôle comment le texte des messages est divisé pour les demandes de TTS. 'Ponctuation' divise en phrases, 'paragraphes' divise en paragraphes et 'aucun' garde le message comme une seule chaîne.", "Controls": "<PERSON><PERSON><PERSON><PERSON>", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text. (Default: 5.0)": "", "Copied": "", "Copied shared chat URL to clipboard!": "URL du chat copiée dans le presse-papiers !", "Copied to clipboard": "", "Copy": "<PERSON><PERSON>", "Copy last code block": "<PERSON><PERSON>r le dernier bloc de code", "Copy last response": "Copier la dernière réponse", "Copy Link": "Copier le lien", "Copy to clipboard": "", "Copying to clipboard was successful!": "La copie dans le presse-papiers a réussi !", "Create": "", "Create a knowledge base": "", "Create a model": "<PERSON><PERSON><PERSON> un modèle", "Create Account": "<PERSON><PERSON><PERSON> un compte", "Create Admin Account": "", "Create Channel": "", "Create Group": "", "Create Knowledge": "", "Create new key": "<PERSON><PERSON>er une nouvelle clé principale", "Create new secret key": "<PERSON><PERSON>er une nouvelle clé secrète", "Created at": "<PERSON><PERSON><PERSON>", "Created At": "<PERSON><PERSON><PERSON>", "Created by": "C<PERSON><PERSON> par", "CSV Import": "Import CSV", "Current Model": "Modèle actuel amélioré", "Current Password": "Mot de passe actuel", "Custom": "Sur mesure", "Dark": "Obscur", "Database": "Base de données", "December": "Décembre", "Default": "<PERSON><PERSON> <PERSON><PERSON>", "Default (Open AI)": "", "Default (SentenceTransformers)": "<PERSON><PERSON> (Sentence Transformers)", "Default Model": "Modèle standard", "Default model updated": "Modèle par défaut mis à jour", "Default Models": "", "Default permissions": "", "Default permissions updated successfully": "", "Default Prompt Suggestions": "Suggestions de prompts par défaut", "Default to 389 or 636 if TLS is enabled": "", "Default to ALL": "", "Default User Role": "Rôle utilisateur par défaut", "Delete": "<PERSON><PERSON><PERSON><PERSON>", "Delete a model": "Supprimer un modèle", "Delete All Chats": "Supp<PERSON><PERSON> toutes les conversations", "Delete All Models": "", "Delete chat": "Supprimer la conversation", "Delete Chat": "Supprimer la Conversation", "Delete chat?": "Supprimer la conversation ?", "Delete folder?": "", "Delete function?": "Supprimer la fonction ?", "Delete Message": "", "Delete prompt?": "Supprimer la prompt ?", "delete this link": "supprimer ce lien", "Delete tool?": "Effacer l'outil ?", "Delete User": "Supprimer le compte d'utilisateur", "Deleted {{deleteModelTag}}": "Supprimé {{deleteModelTag}}", "Deleted {{name}}": "Supprimé {{name}}", "Deleted User": "", "Describe your knowledge base and objectives": "", "Description": "Description", "Disabled": "", "Discover a function": "Découvrez une fonction", "Discover a model": "Découvrir un modèle", "Discover a prompt": "Découvrir une suggestion", "Discover a tool": "Découvrez un outil", "Discover wonders": "", "Discover, download, and explore custom functions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, téléchargez et explorez des fonctions personnalisées", "Discover, download, and explore custom prompts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, té<PERSON>chargez et explorez des prompts personnalisés", "Discover, download, and explore custom tools": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, téléchargez et explorez des outils personnalisés", "Discover, download, and explore model presets": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, télécharger et explorer des préréglages de modèles", "Dismissible": "Fermeture", "Display": "", "Display Emoji in Call": "Afficher les emojis pendant l'appel", "Display the username instead of You in the Chat": "Afficher le nom d'utilisateur à la place de \"Vous\" dans le Chat", "Displays citations in the response": "", "Dive into knowledge": "", "Do not install functions from sources you do not fully trust.": "", "Do not install tools from sources you do not fully trust.": "", "Document": "Document", "Documentation": "Documentation", "Documents": "Documents", "does not make any external connections, and your data stays securely on your locally hosted server.": "ne fait aucune connexion externe et garde vos données en sécurité sur votre serveur local.", "Don't have an account?": "Vous n'avez pas de compte ?", "don't install random functions from sources you don't trust.": "", "don't install random tools from sources you don't trust.": "", "Done": "<PERSON><PERSON><PERSON><PERSON>", "Download": "Télécharger", "Download canceled": "Téléchargement annulé", "Download Database": "Télécharger la base de données", "Drag and drop a file to upload or select a file to view": "", "Draw": "", "Drop any files here to add to the conversation": "Déposez des fichiers ici pour les ajouter à la conversation", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "par ex. '30s', '10 min'. Les unités de temps valides sont 's', 'm', 'h'.", "e.g. A filter to remove profanity from text": "", "e.g. My Filter": "", "e.g. My Tools": "", "e.g. my_filter": "", "e.g. my_tools": "", "e.g. Tools for performing various operations": "", "Edit": "Modifier", "Edit Arena Model": "", "Edit Channel": "", "Edit Connection": "", "Edit Default Permissions": "", "Edit Memory": "Modifier la mémoire", "Edit User": "Modifier l'utilisateur", "Edit User Group": "", "ElevenLabs": "", "Email": "E-mail", "Embark on adventures": "", "Embedding Batch Size": "<PERSON><PERSON> du lot d'encodage", "Embedding Model": "<PERSON><PERSON><PERSON><PERSON> d'embedding", "Embedding Model Engine": "Moteur de modèle d'encodage", "Embedding model set to \"{{embedding_model}}\"": "Modèle d'encodage défini sur « {{embedding_model}} »", "Enable API Key": "", "Enable autocomplete generation for chat messages": "", "Enable Community Sharing": "Activer le partage communautaire", "Enable Google Drive": "", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "", "Enable Message Rating": "", "Enable Mirostat sampling for controlling perplexity. (Default: 0, 0 = Disabled, 1 = Mirostat, 2 = Mirostat 2.0)": "", "Enable New Sign Ups": "Activer les nouvelles inscriptions", "Enable Web Search": "Activer la recherche sur le Web", "Enabled": "", "Engine": "<PERSON><PERSON><PERSON>", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "Vérifiez que votre fichier CSV comprenne les 4 colonnes dans cet ordre : Name, Email, Password, Role.", "Enter {{role}} message here": "Entrez le message {{role}} ici", "Enter a detail about yourself for your LLMs to recall": "Saisissez un détail sur vous-même que vos LLMs pourront se rappeler", "Enter api auth string (e.g. username:password)": "Entrez la chaîne d'authentification de l'API (par ex. nom d'utilisateur:mot de passe)", "Enter Application DN": "", "Enter Application DN Password": "", "Enter Bing Search V7 Endpoint": "", "Enter Bing Search V7 Subscription Key": "", "Enter Brave Search API Key": "Entrez la clé API Brave Search", "Enter certificate path": "", "Enter CFG Scale (e.g. 7.0)": "", "Enter Chunk Overlap": "Entrez le chevauchement de chunk", "Enter Chunk Size": "Entrez la taille de bloc", "Enter description": "", "Enter Github Raw URL": "Entrez l'URL brute de GitHub", "Enter Google PSE API Key": "Entrez la clé API Google PSE", "Enter Google PSE Engine Id": "Entrez l'identifiant du moteur Google PSE", "Enter Image Size (e.g. 512x512)": "Entrez la taille de l'image (par ex. 512x512)", "Enter Jina API Key": "", "Enter Kagi Search API Key": "", "Enter language codes": "Entrez les codes de langue", "Enter Model ID": "", "Enter model tag (e.g. {{modelTag}})": "Entrez l'étiquette du modèle (par ex. {{modelTag}})", "Enter Mojeek Search API Key": "", "Enter name or email": "", "Enter Number of Steps (e.g. 50)": "Entrez le nombre de pas (par ex. 50)", "Enter proxy URL (e.g. **************************:port)": "", "Enter Sampler (e.g. Euler a)": "", "Enter Scheduler (e.g. Karras)": "", "Enter Score": "Entrez votre score", "Enter SearchApi API Key": "", "Enter SearchApi Engine": "", "Enter Searxng Query URL": "Entrez l'URL de la requête Searxng", "Enter Seed": "", "Enter Serper API Key": "Entrez la clé API Serper", "Enter Serply API Key": "Entrez la clé API Serply", "Enter Serpstack API Key": "Entrez la clé API Serpstack", "Enter server host": "", "Enter server label": "", "Enter server port": "", "Enter stop sequence": "Entrez la séquence d'arrê<PERSON>", "Enter system prompt": "", "Enter Tavily API Key": "Entrez la clé API Tavily", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter Tika Server URL": "", "Enter Top K": "Entrez les Top K", "Enter URL (e.g. http://127.0.0.1:7860/)": "Entrez l'URL (par ex. {http://127.0.0.1:7860/})", "Enter URL (e.g. http://localhost:11434)": "Entrez l'URL (par ex. http://localhost:11434)", "Enter your current password": "", "Enter Your Email": "Entrez votre adresse e-mail", "Enter Your Full Name": "Entrez votre nom complet", "Enter your message": "", "Enter your new password": "", "Enter Your Password": "Entrez votre mot de passe", "Enter your prompt": "", "Enter Your Role": "Entrez votre rôle", "Enter Your Username": "", "Enter your webhook URL": "", "Error": "<PERSON><PERSON><PERSON>", "ERROR": "", "Error accessing Google Drive: {{error}}": "", "Error uploading file: {{error}}": "", "Evaluations": "", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "", "Example: ALL": "", "Example: ou=users,dc=foo,dc=example": "", "Example: sAMAccountName or uid or userPrincipalName": "", "Exclude": "", "Experimental": "Expérimental", "Explore the cosmos": "", "Export": "Exportation", "Export All Archived Chats": "", "Export All Chats (All Users)": "Exporter toutes les conversations (tous les utilisateurs)", "Export chat (.json)": "Exporter la discussion (.json)", "Export Chats": "Exporter les conversations", "Export Config to JSON File": "", "Export Functions": "Exportez les Fonctions", "Export Models": "Exporter les modèles", "Export Presets": "", "Export Prompts": "Exporter les Prompts", "Export to CSV": "", "Export Tools": "Outils d'exportation", "External Models": "<PERSON><PERSON><PERSON><PERSON> externes", "Extremely bad": "", "Failed to add file.": "", "Failed to create API Key.": "Échec de la création de la clé API.", "Failed to read clipboard contents": "Échec de la lecture du contenu du presse-papiers", "Failed to save models configuration": "", "Failed to update settings": "Échec de la mise à jour des paramètres", "February": "<PERSON><PERSON><PERSON><PERSON>", "Feedback History": "", "Feedbacks": "", "File": "<PERSON><PERSON><PERSON>", "File added successfully.": "", "File content updated successfully.": "", "File Mode": "<PERSON> fichier", "File not found.": "Fichier introuvable.", "File removed successfully.": "", "File size should not exceed {{maxSize}} MB.": "", "File uploaded successfully": "", "Files": "", "Filter is now globally disabled": "Le filtre est maintenant désactivé globalement", "Filter is now globally enabled": "Le filtre est désormais activé globalement", "Filters": "Filtres", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Spoofing détecté : impossible d'utiliser les initiales comme avatar. Retour à l'image de profil par défaut.", "Fluidly stream large external response chunks": "Diffuser de manière fluide de larges portions de réponses externes", "Focus chat input": "Se concentrer sur le chat en entrée", "Folder deleted successfully": "", "Folder name cannot be empty": "", "Folder name cannot be empty.": "", "Folder name updated successfully": "", "Forge new paths": "", "Form": "Formulaire", "Format your variables using brackets like this:": "", "Frequency Penalty": "Pénalité de fréquence", "Function": "", "Function created successfully": "La fonction a été créée avec succès", "Function deleted successfully": "Fonction supprimée avec succès", "Function Description": "", "Function ID": "", "Function is now globally disabled": "", "Function is now globally enabled": "", "Function Name": "", "Function updated successfully": "La fonction a été mise à jour avec succès", "Functions": "Fonctions", "Functions allow arbitrary code execution": "", "Functions allow arbitrary code execution.": "", "Functions imported successfully": "Fonctions importées avec succès", "General": "Général", "General Settings": "Paramètres Généraux", "Generate Image": "<PERSON><PERSON><PERSON>rer une image", "Generating search query": "Génération d'une requête de recherche", "Get started": "", "Get started with {{WEBUI_NAME}}": "", "Global": "Mondial", "Good Response": "Bonne réponse", "Google Drive": "", "Google PSE API Key": "Clé API Google PSE", "Google PSE Engine Id": "ID du moteur de recherche personnalisé de Google", "Group created successfully": "", "Group deleted successfully": "", "Group Description": "", "Group Name": "", "Group updated successfully": "", "Groups": "", "GSA Chat can make mistakes. Review all responses for accuracy.": "", "h:mm a": "h:mm a", "Haptic Feedback": "", "Harmful or offensive": "", "has no conversations.": "n'a aucune conversation.", "Hello, {{name}}": "<PERSON><PERSON><PERSON>, {{name}}.", "Help": "Aide", "Help us create the best community leaderboard by sharing your feedback history!": "", "Hex Color": "", "Hex Color - Leave empty for default color": "", "Hide": "<PERSON><PERSON>", "Host": "", "How can I help you today?": "Comment puis-je vous être utile aujourd'hui ?", "How would you rate this response?": "", "Hybrid Search": "Recherche hybride", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "", "ID": "", "Ignite curiosity": "", "Image Compression": "", "Image Generation (Experimental)": "Génération d'images (expérimental)", "Image Generation Engine": "Moteur de génération d'images", "Image Max Compression Size": "", "Image Settings": "Paramètres de l'image", "Images": "Images", "Import Chats": "Importer les discussions", "Import Config from JSON File": "", "Import Functions": "Import de fonctions", "Import Models": "Importer des modèles", "Import Presets": "", "Import Prompts": "Importer des Enseignes", "Import Tools": "Outils d'importation", "Include": "", "Include `--api-auth` flag when running stable-diffusion-webui": "<PERSON><PERSON><PERSON> le drapeau `--api-auth` lors de l'exécution de stable-diffusion-webui", "Include `--api` flag when running stable-diffusion-webui": "<PERSON><PERSON><PERSON> le drapeau `--api` lorsque vous exécutez stable-diffusion-webui", "Incomplete response": "", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive. (Default: 0.1)": "", "Info": "Info", "Input commands": "Entrez les commandes", "Install from Github URL": "Installer depuis l'URL GitHub", "Instant Auto-Send After Voice Transcription": "Envoi automatique instantané après transcription vocale", "Interface": "Interface utilisateur", "Invalid file format.": "", "Invalid Tag": "Étiquette non valide", "is typing...": "", "January": "<PERSON><PERSON>", "Jina API Key": "", "join our Discord for help.": "<PERSON><PERSON><PERSON><PERSON> notre Discord pour obtenir de l'aide.", "JSON": "JSON", "JSON Preview": "Aperçu JSON", "July": "<PERSON><PERSON><PERSON>", "June": "Juin", "JWT Expiration": "Expiration du jeton JWT", "JWT Token": "Jeton JWT", "Kagi Search API Key": "", "Keep Alive": "<PERSON><PERSON> connect<PERSON>", "Key": "", "Keyboard shortcuts": "<PERSON><PERSON><PERSON><PERSON> clav<PERSON>", "Knowledge": "Connaissance", "Knowledge Access": "", "Knowledge created successfully.": "", "Knowledge deleted successfully.": "", "Knowledge reset successfully.": "", "Knowledge updated successfully": "", "Label": "", "Landing Page Mode": "", "Language": "<PERSON><PERSON>", "Last Active": "Dernière activité", "Last Modified": "Dernière modification", "Last reply": "", "Latest users": "", "LDAP": "", "LDAP server updated": "", "Leaderboard": "", "Leave empty for unlimited": "", "Leave empty to include all models from \"{{URL}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{URL}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "", "Leave empty to use the default prompt, or enter a custom prompt": "", "Light": "<PERSON><PERSON><PERSON>", "Listening...": "En train d'écouter...", "Local": "", "Local Models": "<PERSON><PERSON><PERSON><PERSON>", "Lost": "", "LTR": "LTR", "Made by OpenWebUI Community": "Réalisé par la communauté OpenWebUI", "Make sure to enclose them with": "Assurez-vous de les inclure dans", "Make sure to export a workflow.json file as API format from ComfyUI.": "", "Manage": "<PERSON><PERSON><PERSON>", "Manage Arena Models": "", "Manage Ollama": "", "Manage Ollama API Connections": "", "Manage OpenAI API Connections": "", "Manage Pipelines": "Gérer les pipelines", "March": "Mars", "Max Tokens (num_predict)": "Tokens maximaux (num_predict)", "Max Upload Count": "", "Max Upload Size": "", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Un maximum de 3 modèles peut être téléchargé en même temps. Veuillez réessayer ultérieurement.", "May": "<PERSON>", "Memories accessible by LLMs will be shown here.": "Les mémoires accessibles par les LLMs seront affichées ici.", "Memory": "M<PERSON><PERSON><PERSON>", "Memory added successfully": "Mémoire ajoutée avec succès", "Memory cleared successfully": "La mémoire a été effacée avec succès", "Memory deleted successfully": "La mémoire a été supprimée avec succès", "Memory updated successfully": "La mémoire a été mise à jour avec succès", "Merge Responses": "", "Message rating should be enabled to use this feature": "", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "Les messages que vous envoyez après avoir créé votre lien ne seront pas partagés. Les utilisateurs disposant de l'URL pourront voir le chat partagé.", "Min P": "", "Minimum Score": "Score minimal", "Mirostat": "Mirostat", "Mirostat Eta": "Mirostat Eta", "Mirostat Tau": "Mirostat Tau", "MMMM DD, YYYY": "DD MMMM YYYY", "MMMM DD, YYYY HH:mm": "DD MMMM YYYY HH:mm", "MMMM DD, YYYY hh:mm:ss A": "DD MMMM YYYY HH:mm:ss", "Model": "", "Model '{{modelName}}' has been successfully downloaded.": "Le modèle '{{modelName}}' a été téléchargé avec succès.", "Model '{{modelTag}}' is already in queue for downloading.": "Le modèle '{{modelTag}}' est déjà dans la file d'attente pour le téléchargement.", "Model {{modelId}} not found": "<PERSON>d<PERSON><PERSON> {{modelId}} introuvable", "Model {{modelName}} is not vision capable": "Le modèle {{modelName}} n'a pas de capacités visuelles", "Model {{name}} is now {{status}}": "Le modèle {{name}} est désormais {{status}}.", "Model accepts image inputs": "", "Model created successfully!": "Le modèle a été créé avec succès !", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Chemin du système de fichiers de modèle détecté. Le nom court du modèle est requis pour la mise à jour, l'opération ne peut pas être poursuivie.", "Model Filtering": "", "Model ID": "ID du modèle", "Model IDs": "", "Model Name": "", "Model not selected": "Modèle non sélectionné", "Model Params": "Paramètres du modèle", "Model Permissions": "", "Model updated successfully": "Le modèle a été mis à jour avec succès", "Modelfile Content": "Contenu du Fichier de Modèle", "Models": "<PERSON><PERSON><PERSON><PERSON>", "Models Access": "", "Models configuration saved successfully": "", "Mojeek Search API Key": "", "more": "", "More": "Plus de", "Name": "Nom", "Name your knowledge base": "", "New Chat": "Nouvelle conversation", "New folder": "", "New Password": "Nouveau mot de passe", "new-channel": "", "No content found": "", "No content to speak": "<PERSON><PERSON>er", "No distance available": "", "No feedbacks found": "", "No file selected": "<PERSON><PERSON><PERSON> fichier s<PERSON>", "No files found.": "", "No groups with access, add a group to grant access": "", "No HTML, CSS, or JavaScript content found.": "", "No knowledge found": "", "No model IDs": "", "No models found": "", "No models selected": "", "No results found": "Aucun résultat trouvé", "No search query generated": "Aucune requête de recherche générée", "No source available": "Aucune source n'est disponible", "No users were found.": "", "No valves to update": "<PERSON><PERSON><PERSON> vanne à mettre à jour", "None": "Aucun", "Not accurate": "", "Not relevant": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Note : Si vous définissez un score minimum, seuls les documents ayant un score supérieur ou égal à ce score minimum seront retournés par la recherche.", "Notes": "", "Notification Sound": "", "Notification Webhook": "", "Notifications": "Notifications", "November": "Novembre", "num_gpu (Ollama)": "", "num_thread (Ollama)": "num_thread (Ollama)", "OAuth ID": "ID OAuth", "October": "Octobre", "Off": "Désactivé", "Okay, Let's Go!": "D'accord, on y va !", "OLED Dark": "Noir OLED", "Ollama": "Ollama", "Ollama API": "API Ollama", "Ollama API disabled": "API Ollama désactivée", "Ollama API settings updated": "", "Ollama Version": "Version Ollama améliorée", "On": "Activé", "Only alphanumeric characters and hyphens are allowed": "", "Only alphanumeric characters and hyphens are allowed in the command string.": "Seuls les caractères alphanumériques et les tirets sont autorisés dans la chaîne de commande.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "", "Only select users and groups with permission can access": "", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Oups ! Il semble que l'URL soit invalide. Veuillez vérifier à nouveau et réessayer.", "Oops! There are files still uploading. Please wait for the upload to complete.": "", "Oops! There was an error in the previous response.": "", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Oups ! Vous utilisez une méthode non prise en charge (frontend uniquement). Veuillez servir l'interface Web à partir du backend.", "Open in full screen": "", "Open new chat": "Ouvrir une nouvelle discussion", "Open WebUI uses faster-whisper internally.": "", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "", "OpenAI": "OpenAI", "OpenAI API": "API OpenAI", "OpenAI API Config": "Configuration de l'API OpenAI", "OpenAI API Key is required.": "Une clé API OpenAI est requise.", "OpenAI API settings updated": "", "OpenAI URL/Key required.": "URL/Clé OpenAI requise.", "or": "ou", "Organize your users": "", "OUTPUT": "", "Output format": "", "Overview": "", "page": "", "Password": "Mot de passe", "Paste Large Text as File": "", "PDF document (.pdf)": "Document au format PDF  (.pdf)", "PDF Extract Images (OCR)": "Extraction d'images PDF (OCR)", "pending": "en attente", "Permission denied when accessing media devices": "Accès aux appareils multimédias refusé", "Permission denied when accessing microphone": "Autorisation refusée lors de l'accès au micro", "Permission denied when accessing microphone: {{error}}": "Permission refusée lors de l'accès au microphone : {{error}}", "Permissions": "", "Personalization": "Personnalisation", "Pin": "<PERSON><PERSON><PERSON>", "Pinned": "<PERSON><PERSON><PERSON>", "Pioneer insights": "", "Pipeline deleted successfully": "Le pipeline a été supprimé avec succès", "Pipeline downloaded successfully": "Le pipeline a été téléchargé avec succès", "Pipelines": "Pipelines", "Pipelines Not Detected": "Aucun pipelines détecté", "Pipelines Valves": "Vannes de Pipelines", "Plain text (.txt)": "Texte simple (.txt)", "Playground": "Aire de jeux", "Please carefully review the following warnings:": "", "Please enter a prompt": "", "Please fill in all fields.": "", "Please select a model first.": "", "Port": "", "Prefix ID": "", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "", "Previous 30 days": "30 derniers jours", "Previous 7 days": "7 derniers jours", "Profile Image": "Image de profil", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Prompt (par ex. Di<PERSON>-moi un fait amusant à propos de l'Empire romain)", "Prompt Content": "Contenu du prompt", "Prompt created successfully": "", "Prompt suggestions": "Suggestions pour le prompt", "Prompt updated successfully": "", "Prompts": "Prompts", "Prompts Access": "", "Provide any specific details": "", "Proxy URL": "", "Pull \"{{searchValue}}\" from Ollama.com": "Ré<PERSON><PERSON>rer « {{searchValue}} » depuis Ollama.com", "Pull a model from Ollama.com": "Télécharger un modèle depuis Ollama.com", "Query Generation Prompt": "", "Query Params": "Paramètres de requête", "RAG Template": "Modèle RAG", "Rating": "", "Re-rank models by topic similarity": "", "Read Aloud": "Lire à haute voix", "Record voice": "Enregistrer la voix", "Redirecting you to OpenWebUI Community": "Redirection vers la communauté OpenWebUI", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative. (Default: 40)": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "Désignez-vous comme « Utilisateur » (par ex. « L'utilisateur apprend l'espagnol »)", "References from": "", "Refresh Token Expiration": "", "Regenerate": "<PERSON><PERSON><PERSON><PERSON>", "Release Notes": "Notes de publication", "Relevance": "", "Remove": "<PERSON><PERSON><PERSON>", "Remove Model": "<PERSON><PERSON><PERSON> le modèle", "Rename": "<PERSON>mmer", "Reorder Models": "", "Repeat Last N": "Répéter les N derniers", "Reply in Thread": "", "Request Mode": "Mode de Requête", "Reranking Model": "Mo<PERSON><PERSON><PERSON> de ré-ranking", "Reranking model disabled": "Mod<PERSON>le de ré-ranking désactivé", "Reranking model set to \"{{reranking_model}}\"": "<PERSON><PERSON><PERSON><PERSON> de ré-ranking défini sur « {{reranking_model}} »", "Reset": "Réinitialiser", "Reset All Models": "", "Reset Upload Directory": "Répertoire de téléchargement réinitialisé", "Reset Vector Storage/Knowledge": "", "Reset view": "", "Response generation stopped": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "Les notifications de réponse ne peuvent pas être activées car les autorisations du site web ont été refusées. V<PERSON><PERSON><PERSON> visiter les paramètres de votre navigateur pour accorder l'accès nécessaire.", "Response splitting": "Fractionnement de la réponse", "Result": "", "Retrieval Query Generation": "", "Rich Text Input for Chat": "", "RK": "", "Role": "R<PERSON><PERSON>", "Rosé Pine": "<PERSON>n r<PERSON>", "Rosé Pine Dawn": "Aube de Pin <PERSON>", "RTL": "RTL", "Run": "", "Running": "<PERSON><PERSON><PERSON>", "Save": "Enregistrer", "Save & Create": "Enregistrer & Créer", "Save & Update": "Enregistrer & Mettre à jour", "Save As Copy": "", "Save Tag": "", "Saved": "", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "La sauvegarde des journaux de discussion directement dans le stockage de votre navigateur n'est plus prise en charge. V<PERSON><PERSON>z prendre un instant pour télécharger et supprimer vos journaux de discussion en cliquant sur le bouton ci-dessous. <PERSON><PERSON> de soucis, vous pouvez facilement les réimporter depuis le backend via l'interface ci-dessous", "Scroll to bottom when switching between branches": "", "Search": "Recherche", "Search a model": "Rechercher un modèle", "Search Base": "", "Search Chats": "Rechercher des conversations", "Search Collection": "", "Search Filters": "", "search for tags": "", "Search Functions": "Fonctions de recherche", "Search Knowledge": "", "Search Models": "Rechercher des modèles", "Search options": "", "Search Prompts": "Recherche de prompts", "Search Result Count": "Nombre de résultats de recherche", "Search the web": "", "Search Tools": "Outils de recherche", "Search users": "", "SearchApi API Key": "", "SearchApi Engine": "", "Searched {{count}} sites": "", "Searching \"{{searchQuery}}\"": "Recherche de « {{searchQuery}} »", "Searching Knowledge for \"{{searchQuery}}\"": "", "Searxng Query URL": "URL de recherche Searxng", "See readme.md for instructions": "Voir le fichier readme.md pour les instructions", "See what's new": "Découvrez les nouvelles fonctionnalités", "Seed": "Graine", "Select a base model": "Sélectionnez un modèle de base", "Select a engine": "Sélectionnez un moteur", "Select a function": "Sélectionnez une fonction", "Select a group": "", "Select a model": "Sélectionnez un modèle", "Select a pipeline": "Sélectionnez un pipeline", "Select a pipeline url": "Sélectionnez l'URL du pipeline", "Select a tool": "Sélectionnez un outil", "Select Engine": "", "Select Knowledge": "", "Select model": "Sélectionnez un modèle", "Select only one model to call": "Sélectionnez seulement un modèle pour appeler", "Selected model(s) do not support image inputs": "Les modèle(s) sélectionné(s) ne prennent pas en charge les entrées d'images", "Semantic distance to query": "", "Send": "Envoyer", "Send a message": "", "Send a Message": "Envoyer un message", "Send message": "Envoyer un message", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "", "September": "Septembre", "Serper API Key": "Clé API Serper", "Serply API Key": "Clé API Serply", "Serpstack API Key": "Clé API Serpstack", "Server connection verified": "Connexion au serveur vérifiée", "Set as default": "Définir comme valeur par défaut", "Set CFG Scale": "", "Set Default Model": "Définir le modèle par défaut", "Set embedding model": "", "Set embedding model (e.g. {{model}})": "Définir le modèle d'encodage (par ex. {{model}})", "Set Image Size": "Définir la taille de l'image", "Set reranking model (e.g. {{model}})": "Définir le modèle de reclassement (par ex. {{model}})", "Set Sampler": "", "Set Scheduler": "", "Set Steps": "Définir les étapes", "Set Task Model": "Définir le modèle de tâche", "Set the number of GPU devices used for computation. This option controls how many GPU devices (if available) are used to process incoming requests. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "", "Set Voice": "Définir la voix", "Set whisper model": "", "Sets how far back for the model to look back to prevent repetition. (Default: 64, 0 = disabled, -1 = num_ctx)": "", "Sets how strongly to penalize repetitions. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. (Default: 1.1)": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt. (Default: random)": "", "Sets the size of the context window used to generate the next token. (Default: 2048)": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "", "Settings": "Paramètres", "Settings saved successfully!": "Paramètres enregistrés avec succès !", "Share": "Partager", "Share Chat": "Partage de conversation", "Share to OpenWebUI Community": "Partager avec la communauté OpenWebUI", "Show": "<PERSON><PERSON>", "Show \"What's New\" modal on login": "", "Show Admin Details in Account Pending Overlay": "Afficher les détails de l'administrateur dans la superposition en attente du compte", "Show shortcuts": "Aff<PERSON>r les raccourcis", "Show your support!": "<PERSON>re ton soutien !", "Sign in": "S'identifier", "Sign in to {{WEBUI_NAME}}": "", "Sign in to {{WEBUI_NAME}} with LDAP": "", "Sign Out": "Déconnexion", "Sign up": "Inscrivez-vous", "Sign up to {{WEBUI_NAME}}": "", "Signing in to {{WEBUI_NAME}}": "", "sk-1234": "", "Source": "Source", "Speech Playback Speed": "", "Speech recognition error: {{error}}": "E<PERSON>ur de reconnaissance vocale : {{error}}", "Speech-to-Text Engine": "Moteur de reconnaissance vocale", "Stop": "", "Stop Sequence": "<PERSON><PERSON><PERSON>", "Stream Chat Response": "", "STT Model": "Modèle de STT", "STT Settings": "Paramètres de STT", "Success": "Réussite", "Successfully updated.": "Mise à jour réussie.", "Suggested prompts to get you started": "", "Support": "", "Support this plugin:": "", "Sync directory": "", "System": "Système", "System Instructions": "", "System Prompt": "Prompt du système", "Tags Generation": "", "Tags Generation Prompt": "", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting. (default: 1)": "", "Tap to interrupt": "Appuyez pour interrompre", "Tavily API Key": "Clé API Tavily", "Temperature": "Température", "Template": "Template", "Temporary Chat": "", "Text Splitter": "", "Text-to-Speech Engine": "Mo<PERSON>ur de synthèse vocale", "Tfs Z": "Tfs Z", "Thanks for your feedback!": "Merci pour vos commentaires !", "The Application Account DN you bind with for search": "", "The base to search for users": "", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.  (Default: 512)": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "", "The LDAP attribute that maps to the username that users use to sign in.": "", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "Le score doit être une valeur comprise entre 0,0 (0 %) et 1,0 (100 %).", "The temperature of the model. Increasing the temperature will make the model answer more creatively. (Default: 0.8)": "", "Theme": "Thème", "Thinking...": "En train de réfléchir...", "This action cannot be undone. Do you wish to continue?": "Cette action ne peut pas être annulée. Souhaitez-vous continuer ?", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "<PERSON><PERSON> garan<PERSON>t que vos conversations précieuses soient sauvegardées en toute sécurité dans votre base de données backend. Merci !", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "Il s'agit d'une fonctionnalité expérimentale, elle peut ne pas fonctionner comme prévu et est sujette à modification à tout moment.", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics. (Default: 24)": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.  (Default: 128)": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "", "This response was generated by \"{{model}}\"": "", "This will delete": "<PERSON><PERSON> supprimera", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "", "This will delete all models including custom models": "", "This will delete all models including custom models and cannot be undone.": "", "This will reset the knowledge base and sync all files. Do you wish to continue?": "", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "URL du serveur Tika requise.", "Tiktoken": "", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Conseil : mettez à jour plusieurs emplacements de variables consécutivement en appuyant sur la touche Tab dans l’entrée de chat après chaque remplacement.", "Title": "Titre", "Title (e.g. Tell me a fun fact)": "Titre (par ex. raconte-moi un fait amusant)", "Title Auto-Generation": "Génération automatique de titres", "Title cannot be an empty string.": "Le titre ne peut pas être une chaîne de caractères vide.", "Title Generation Prompt": "Prompt de génération de titre", "TLS": "", "To access the available model names for downloading,": "Pour accéder aux noms des modèles disponibles en téléchargement,", "To access the GGUF models available for downloading,": "Pour accéder aux modèles GGUF disponibles en téléchargement,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "Pour accéder à l'interface Web, veuillez contacter l'administrateur. Les administrateurs peuvent gérer les statuts des utilisateurs depuis le panneau d'administration.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "", "To learn more about available endpoints, visit our documentation.": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "", "To select actions here, add them to the \"Functions\" workspace first.": "", "To select filters here, add them to the \"Functions\" workspace first.": "Pour sélectionner des filtres ici, ajoutez-les d'abord à l'espace de travail « Fonctions ». ", "To select toolkits here, add them to the \"Tools\" workspace first.": "Pour sélectionner des toolkits ici, ajoutez-les d'abord à l'espace de travail « Outils ». ", "Toast notifications for new updates": "", "Today": "<PERSON><PERSON><PERSON>'hui", "Toggle settings": "Basculer les paramètres", "Toggle sidebar": "Basculer la barre latérale", "Token": "", "Tokens To Keep On Context Refresh (num_keep)": "Jeton à conserver pour l'actualisation du contexte (num_keep)", "Tool created successfully": "L'outil a été créé avec succès", "Tool deleted successfully": "Outil supprimé avec succès", "Tool Description": "", "Tool ID": "", "Tool imported successfully": "Outil importé avec succès", "Tool Name": "", "Tool updated successfully": "L'outil a été mis à jour avec succès", "Tools": "Outils", "Tools Access": "", "Tools are a function calling system with arbitrary code execution": "", "Tools have a function calling system that allows arbitrary code execution": "", "Tools have a function calling system that allows arbitrary code execution.": "", "Top K": "Top K", "Top P": "Top P", "Transformers": "", "Trouble accessing Ollama?": "Rencontrez-vous des difficultés pour accéder à Ollama ?", "TTS Model": "<PERSON><PERSON><PERSON><PERSON> de synthèse vocale", "TTS Settings": "Paramètres de synthèse vocale", "TTS Voice": "Voix TTS", "Type": "Type", "Type Hugging Face Resolve (Download) URL": "Entrez l'URL de Téléchargement Hugging Face Resolve", "Uh-oh! There was an issue with the response.": "", "UI": "Interface utilisateur", "Unarchive All": "", "Unarchive All Archived Chats": "", "Unarchive Chat": "", "Unlock mysteries": "", "Unpin": "", "Unravel secrets": "", "Untagged": "", "Update": "Mise à jour", "Update and Copy Link": "Mettre à jour et copier le lien", "Update for the latest features and improvements.": "", "Update password": "Mettre à jour le mot de passe", "Updated": "", "Updated at": "Mise à jour le", "Updated At": "", "Upload": "Télécharger", "Upload a GGUF model": "Téléverser un modèle GGUF", "Upload directory": "", "Upload files": "", "Upload Files": "Télécharger des fichiers", "Upload Pipeline": "Pipeline de téléchargement", "Upload Progress": "Progression de l'envoi", "URL": "", "URL Mode": "Mode d'URL", "Use '#' in the prompt input to load and include your knowledge.": "", "Use Gravatar": "<PERSON><PERSON><PERSON><PERSON>", "Use groups to group your users and assign permissions.": "", "Use Initials": "Utiliser les initiales", "use_mlock (Ollama)": "use_mlock (Ollama)", "use_mmap (Ollama)": "utiliser mmap (Ollama)", "user": "utilisateur", "User": "", "User location successfully retrieved.": "L'emplacement de l'utilisateur a été récupéré avec succès.", "Username": "", "Users": "Utilisateurs", "Using the default arena model with all models. Click the plus button to add custom models.": "", "Utilize": "<PERSON><PERSON><PERSON><PERSON>", "Valid time units:": "Unités de temps valides :", "Valves": "<PERSON><PERSON>", "Valves updated": "<PERSON><PERSON> mises à jour", "Valves updated successfully": "Les vannes ont été mises à jour avec succès", "variable": "variable", "variable to have them replaced with clipboard content.": "variable pour qu'elles soient remplacées par le contenu du presse-papiers.", "Version": "Version améliorée", "Version {{selectedVersion}} of {{totalVersions}}": "", "Very bad": "", "View Replies": "", "Visibility": "", "Voice": "Voix", "Voice Input": "", "Warning": "Avertissement !", "Warning:": "", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Avertissement : Si vous mettez à jour ou modifiez votre modèle d'encodage, vous devrez réimporter tous les documents.", "Web": "Web", "Web API": "API Web", "Web Loader Settings": "Paramètres du chargeur web", "Web Search": "Recherche Web", "Web Search Engine": "Moteur de recherche Web", "Web Search Query Generation": "", "Webhook URL": "URL du webhook", "WebUI Settings": "Paramètres de WebUI", "WebUI URL": "", "WebUI will make requests to \"{{url}}/api/chat\"": "", "WebUI will make requests to \"{{url}}/chat/completions\"": "", "What are you trying to achieve?": "", "What are you working on?": "", "What didn't you like about this response?": "", "What’s New in": "<PERSON><PERSON><PERSON> de <PERSON>uf", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "", "wherever you are": "", "Whisper (Local)": "<PERSON><PERSON><PERSON> (local)", "Widescreen Mode": "Mode Grand Écran", "Won": "", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text. (Default: 0.9)": "", "Workspace": "Espace de travail", "Workspace Permissions": "", "Write a prompt suggestion (e.g. Who are you?)": "<PERSON><PERSON><PERSON>z une suggestion de prompt (par exemple : Qui êtes-vous ?)", "Write a summary in 50 words that summarizes [topic or keyword].": "Rédigez un résumé de 50 mots qui résume [sujet ou mot-clé].", "Write something...": "", "Write your model template content here": "", "Yesterday": "<PERSON>er", "You": "Vous", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "Vous pouvez personnaliser vos interactions avec les LLM en ajoutant des souvenirs via le bouton 'Gérer' ci-dessous, ce qui les rendra plus utiles et adaptés à vos besoins.", "You cannot upload an empty file.": "", "You have no archived conversations.": "Vous n'avez aucune conversation archivée", "You have shared this chat": "Vous avez partagé cette conversation.", "You're a helpful assistant.": "Vous êtes un assistant serviable.", "Your account status is currently pending activation.": "Votre statut de compte est actuellement en attente d'activation.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "", "Youtube": "YouTube", "Youtube Loader Settings": "Paramètres de l'outil de téléchargement YouTube"}