[tool:pytest]
xfail_strict=true

[black]
line-length = 120
target-version = ['py38', 'py310', 'py311']
skip-string-normalization = true
exclude = venv*,__pycache__,node_modules,cache,migrations,build,sample_cap_xml_documents.py

[flake8]
exclude = venv*,__pycache__,node_modules,cache,migrations,build,sample_cap_xml_documents.py
max-line-length = 120
extend_ignore = B306, W504, F541, E203, E302, E305
per-file-ignores =
    .env: E501

[isort]
profile = black
multi_line_output = 3

[coverage:run]
omit =
    # omit anything in a .local directory anywhere
    */.local/*
    # omit everything in /usr
    /usr/*
    */tests/*
    */virtualenvs/*
    */migrations/*
