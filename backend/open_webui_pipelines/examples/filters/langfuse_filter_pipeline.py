# """
# title: Langfuse Filter Pipeline
# author: open-webui
# date: 2024-05-30
# version: 1.1
# license: MIT
# description: A filter pipeline that uses Langfuse.
# requirements: langfuse
# """

# from typing import List, Optional
# from schemas import OpenAIChatMessage
# import os

# from utils.pipelines.main import get_last_user_message, get_last_assistant_message
# from pydantic import BaseModel
# from langfuse import Langfuse
# from langfuse.api.resources.commons.errors.unauthorized_error import UnauthorizedError


# class Pipeline:
#     class Valves(BaseModel):
#         # List target pipeline ids (models) that this filter will be connected to.
#         # If you want to connect this filter to all pipelines, you can set pipelines to ["*"]
#         # e.g. ["llama3:latest", "gpt-3.5-turbo"]
#         pipelines: List[str] = []

#         # Assign a priority level to the filter pipeline.
#         # The priority level determines the order in which the filter pipelines are executed.
#         # The lower the number, the higher the priority.
#         priority: int = 0

#         # Valves
#         secret_key: str
#         public_key: str
#         host: str

#     def __init__(self):
#         # Pipeline filters are only compatible with Open WebUI
#         # You can think of filter pipeline as a middleware that can be used to edit the form data before it is sent to the OpenAI API.
#         self.type = "filter"

#         # Optionally, you can set the id and name of the pipeline.
#         # Best practice is to not specify the id so that it can be automatically inferred from the filename, so that users can install multiple versions of the same pipeline.
#         # The identifier must be unique across all pipelines.
#         # The identifier must be an alphanumeric string that can include underscores or hyphens. It cannot contain spaces, special characters, slashes, or backslashes.
#         # self.id = "langfuse_filter_pipeline"
#         self.name = "Langfuse Filter"

#         # Initialize
#         self.valves = self.Valves(
#             **{
#                 "pipelines": ["*"],  # Connect to all pipelines
#                 "secret_key": os.getenv("LANGFUSE_SECRET_KEY", "your-secret-key-here"),
#                 "public_key": os.getenv("LANGFUSE_PUBLIC_KEY", "your-public-key-here"),
#                 "host": os.getenv("LANGFUSE_HOST", "https://cloud.langfuse.com"),
#             }
#         )

#         self.langfuse = None
#         self.chat_generations = {}
#         pass

#     async def on_startup(self):
#         # This function is called when the server is started.
#         print(f"on_startup:{__name__}")
#         self.set_langfuse()
#         pass

#     async def on_shutdown(self):
#         # This function is called when the server is stopped.
#         print(f"on_shutdown:{__name__}")
#         self.langfuse.flush()
#         pass

#     async def on_valves_updated(self):
#         # This function is called when the valves are updated.

#         self.set_langfuse()
#         pass

#     def set_langfuse(self):
#         try:
#             self.langfuse = Langfuse(
#                 secret_key=self.valves.secret_key,
#                 public_key=self.valves.public_key,
#                 host=self.valves.host,
#                 debug=False,
#             )
#             self.langfuse.auth_check()
#         except UnauthorizedError:
#             print(
#                 "Langfuse credentials incorrect. Please re-enter your Langfuse credentials in the pipeline settings."
#             )
#         except Exception as e:
#             print(f"Langfuse error: {e} Please re-enter your Langfuse credentials in the pipeline settings.")

#     async def inlet(self, body: dict, user: Optional[dict] = None) -> dict:
#         print(f"inlet:{__name__}")

#         trace = self.langfuse.trace(
#             name=f"filter:{__name__}",
#             input=body,
#             user_id=user["id"],
#             metadata={"name": user["name"]},
#             session_id=body["chat_id"],
#         )

#         generation = trace.generation(
#             name=body["chat_id"],
#             model=body["model"],
#             input=body["messages"],
#             metadata={"interface": "open-webui"},
#         )

#         self.chat_generations[body["chat_id"]] = generation
#         print(trace.get_trace_url())

#         return body

#     async def outlet(self, body: dict, user: Optional[dict] = None) -> dict:
#         print(f"outlet:{__name__}")
#         if body["chat_id"] not in self.chat_generations:
#             return body

#         generation = self.chat_generations[body["chat_id"]]

#         user_message = get_last_user_message(body["messages"])
#         generated_message = get_last_assistant_message(body["messages"])

#         # Update usage cost based on the length of the input and output messages
#         # Below does not reflect the actual cost of the API
#         # You can adjust the cost based on your requirements
#         generation.end(
#             output=generated_message,
#             usage={
#                 "totalCost": (len(user_message) + len(generated_message)) / 1000,
#                 "unit": "CHARACTERS",
#             },
#             metadata={"interface": "open-webui"},
#         )

#         return body
